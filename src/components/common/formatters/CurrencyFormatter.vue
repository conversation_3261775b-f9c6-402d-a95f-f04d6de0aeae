<template>
  <span>{{ formattedAmount }}</span>
</template>

<script>
export default {
  props: {
    // Number to format, e.g. 1234.56
    value: {
      type: Number,
      required: true,
    },
    // Currency symbol, e.g. "zł"
    symbol: {
      type: String,
      required: true,
    },
  },
  computed: {
    formattedAmount() {
      // Format number with 2 decimal places and thousand separators
      const formattedNumber = this.value.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
      // Combine with provided symbol
      return `${formattedNumber} ${this.symbol}`;
    },
  },
};
</script>
