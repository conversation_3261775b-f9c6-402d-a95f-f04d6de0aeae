<template>
  <span>{{ formattedDate }}</span>
</template>

<script>
import moment from 'moment/moment';

export default {
  props: {
    value: {
      type: String,
      default: null,
    },
    format: {
      type: String,
      default: 'YYYY-MM-DD HH:mm',
    },
  },
  computed: {
    formattedDate() {
      if (!this.value) return '';
      const date = moment(this.value);
      return date.format(this.format);
    },
  },
};
</script>
