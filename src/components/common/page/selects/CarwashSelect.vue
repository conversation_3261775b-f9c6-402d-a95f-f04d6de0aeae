<template>
  <v-autocomplete
    key="carwash"
    ref="carwashs"
    v-model="internalValue"
    :disabled="disabled"
    :items="carwashesOptions"
    item-value="serialNumber"
    item-text="longName"
    prepend-icon="mdi-car-wash"
    :label="$t('common_filtersCarwash')"
  />
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    value: {
      type: [String, Number, null], // lub odpowiedni typ dla serialNumber
      default: null,
    },
  },
  computed: {
    ...mapGetters({
      carwashes: 'carwashes/carwashes',
    }),
    carwashesOptions() {
      const all = {
        serialNumber: null,
        longName: this.$t('common_all'),
      };
      return [all, ...this.carwashes];
    },
    internalValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
        this.$emit('change', val); // opcjonalnie, je<PERSON><PERSON> ch<PERSON>z te<PERSON> @change
      },
    },
  },
};
</script>
