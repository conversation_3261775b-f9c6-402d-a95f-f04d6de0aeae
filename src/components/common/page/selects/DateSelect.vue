<template>
  <div class="d-flex flex-column">
    <div
      v-if="showPresets"
      class="py-0"
    >
      <v-select
        v-model="currentPreset"
        color="text"
        dense
        class="d-flex"
        :items="availablePresets"
        :label="label"
        :menu-props="{maxHeight: 500}"
        :disabled="disabled"
        @change="onPresetChange"
      />
    </div>
    <v-scroll-y-transition>
      <div
        v-if="showDatePicker"
        class="py-0"
      >
        <v-dialog
          ref="dialog"
          v-model="dateRangeModal"
          width="290px"
        >
          <template #activator="{ on, attrs }">
            <v-text-field
              v-model="dateRangeFormatted"
              :label="$t('common_inPeriodCustom')"
              readonly
              :disabled="disabled"
              :prepend-icon="prependIcon"
              v-bind="attrs"
              v-on="on"
            />
          </template>
          <v-date-picker
            v-model="dateRange"
            range
            :locale="locale"
            scrollable
            :min="dateMin"
            :max="dateMax"
            class="dateRange"
            :disabled="disabled"
            :selected-items-text="$t('common_selected')"
            @change="swapDates"
          >
            <v-spacer />
            <v-btn
              text
              @click="cancel"
            >
              {{ $t('actions.cancel') }}
            </v-btn>
            <v-btn
              text
              color="primary"
              @click="confirm"
            >
              {{ $t('actions.chose') }}
            </v-btn>
          </v-date-picker>
        </v-dialog>
      </div>
    </v-scroll-y-transition>
  </div>
</template>

<script>
import {
  endOfToday,
  endOfYesterday,
  startOfMonth,
  startOfToday,
  startOfYesterday,
  format,
  startOfYear,
  subDays,
  startOfDay,
  subMonths,
  subYears,
  endOfMonth,
  endOfYear, startOfWeek, endOfWeek, subWeeks,
} from 'date-fns';

export default {
  props: {
    label: {
      type: String,
      default() {
        return this.$t('common_inPeriod');
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    presets: {
      type: Array,
      default() {
        return [
          {
            value: 'today',
            text: this.$t('common_today'),
            start: startOfToday(),
            end: endOfToday(),
          },
          {
            value: 'yesterday',
            text: this.$t('common_daterangeYesterday'),
            start: startOfYesterday(),
            end: endOfYesterday(),
          },
          {
            value: 'thisWeek',
            text: this.$t('thisWeek'),
            start: startOfWeek(startOfDay(new Date())),
            end: endOfToday(),
            default: true,
          },
          {
            value: 'lastWeek',
            text: this.$t('lastWeek'),
            start: startOfWeek(subWeeks(new Date(), 1)),
            end: endOfWeek(subWeeks(new Date(), 1)),
            default: true,
          },
          {
            value: 'thisMonth',
            text: this.$t('common_sinceMonthStart'),
            start: startOfMonth(new Date()),
            end: endOfToday(),
          },
          {
            value: 'lastMonth',
            text: this.$t('common_previousMonth'),
            start: startOfMonth(subMonths(new Date(), 1)),
            end: endOfMonth(subMonths(new Date(), 1)),
          },
          {
            value: 'thisYear',
            text: this.$t('common_currentYear'),
            start: startOfYear(new Date()),
            end: endOfToday(),
          },
          {
            value: 'lastYear',
            text: this.$t('common_previousYear'),
            start: startOfYear(subYears(new Date(), 1)),
            end: endOfYear(subYears(new Date(), 1)),
          },
        ];
      },
    },
    showPresets: {
      type: Boolean,
      default: true,
    },
    showLastCollection: {
      type: Boolean,
      default: false,
    },
    showCustom: {
      type: Boolean,
      default: true,
    },
    startDateRange: {
      type: Array,
      default() {
        return [
          format(subDays(new Date(), 7), 'YYYY-MM-DD'),
          format(new Date(), 'YYYY-MM-DD'),
        ];
      },
    },
    startPreset: {
      type: String,
      default: 'thisWeek',
    },
    format: {
      type: String,
      default: 'YYYY-MM-DD HH:mm:ss',
    },
    dateMinimum: {
      type: String,
      default: '2006-01-01',
    },
    dateMaximum: {
      type: String,
      default: () => new Date().toISOString().slice(0, 10),
    },
  },
  data() {
    return {
      current: 'thisWeek',
      locale: this.$i18n.locale,
      dateRangeModal: false,
      dateRangeOld: [format(subDays(new Date(), 7), 'YYYY-MM-DD'), format(new Date(), 'YYYY-MM-DD')],
      dateRange: this.startDateRange,
      dateMin: this.dateMin,
      dateMax: this.dateMaximum,
    };
  },
  computed: {
    currentPreset: {
      get() {
        if (this.current != null) {
          return this.current;
        }

        if (!this.showPresets || !this.presets.length) {
          return null;
        }

        const defaultPresets = this.presets.filter((preset) => ('value' in preset) && preset.value === this.startPreset);

        if (!defaultPresets.length) {
          return this.presets[0].value;
        }

        return defaultPresets[0].value;
      },
      set(value) {
        this.current = value;
      },
    },
    showDatePicker() {
      return (this.showPresets && this.currentPreset === 'custom') || !this.showPresets;
    },
    availablePresets() {
      const availablePresets = [...this.presets];
      if (this.showLastCollection) {
        availablePresets.unshift({
          value: 'lastCollection',
          text: this.$t('common_last'),
        });
      }
      if (this.showCustom) {
        availablePresets.push({
          value: 'custom',
          text: this.$t('common_custom'),
        });
      }

      return availablePresets;
    },
    today() {
      return new Date().toISOString()
        .slice(0, 10);
    },
    dateRangeFormatted() {
      let dateRangeSecond;
      if (this.dateRange[1]) {
        if (this.dateRange[1] === this.dateRange[0]) {
          return this.$options.filters.formatDateDay(this.dateRange[0]);
        }
        [, dateRangeSecond] = this.dateRange;
        dateRangeSecond = this.$options.filters.formatDateDay(dateRangeSecond);
      } else {
        dateRangeSecond = '...';
      }

      return `${this.$options.filters.formatDateDay(this.dateRange[0])} – ${dateRangeSecond}`;
    },
  },
  watch: {
    presets(newPresets) {
      const newPresetsValues = newPresets.map((p) => p.value);
      if (newPresetsValues.indexOf(this.currentPreset) > -1) {
        this.onPresetChange(this.currentPreset);
        return;
      }
      this.resetPreset();
    },
    showLastCollection(newValue) {
      if (!newValue && this.currentPreset === 'lastCollection') {
        if (!this.presets.length) {
          return;
        }
        this.resetPreset();
      }
    },
  },
  mounted() {
    if (this.showPresets) {
      this.currentPreset = this.getStartingPreset();
      this.onPresetChange(this.currentPreset);
    } else {
      this.confirm();
    }
  },
  methods: {
    resetPreset() {
      this.currentPreset = this.getStartingPreset();
      this.onPresetChange(this.getStartingPreset());
    },
    onPresetChange(value) {
      const item = this.getPresetByValue(value);
      this.current = item.value;

      if (!item) {
        return;
      }
      if (item.value === 'all') {
        this.$emit('change', {
          from: null,
          to: null,
          value,
          isLastCollection: false,
        });
        return;
      }
      if (item.value === 'lastCollection') {
        this.$emit('change', {
          from: null,
          to: null,
          value,
          isLastCollection: true,
        });
        return;
      }
      if (!('start' in item)) {
        return;
      }
      this.dateRange.splice(0, 1, format(item.start, this.format));
      this.dateRange.splice(1, 1, format(item.end, this.format));
      this.confirm();
    },
    getPresetByValue(value) {
      const presets = this.availablePresets.filter((preset) => preset.value === value);

      return presets[0] ?? null;
    },
    swapDates() {
      if (this.dateRange[0] > this.dateRange[1]) {
        const temp = this.dateRange[0];
        this.dateRange.splice(0, 1, this.dateRange[1]);
        this.dateRange.splice(1, 1, temp);
      }
    },
    confirm() {
      if (!this.dateRange[0] || !this.dateRange[1]) {
        this.cancel();
        return;
      }
      this.dateRangeOld = this.dateRange;
      this.closeDateModal();
      this.$emit('change', {
        value: this.current ?? undefined,
        from: this.dateRange[0],
        to: this.dateRange[1],
        isLastCollection: false,
      });
    },
    cancel() {
      this.dateRange = this.dateRangeOld;
      this.closeDateModal();
    },
    closeDateModal() {
      this.dateRangeModal = false;
    },
    getStartingPreset() {
      if (!this.showPresets || !this.presets.length) {
        return null;
      }

      const defaultPresets = this.presets.filter((preset) => ('value' in preset) && preset.value === this.startPreset);

      if (!defaultPresets.length) {
        return this.presets[0].value;
      }
      return defaultPresets[0].value;
    },
  },
};
</script>
