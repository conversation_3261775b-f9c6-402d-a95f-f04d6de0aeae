<template>
  <v-text-field
    :label="$t('common_search')"
    prepend-icon="mdi-magnify"
    :disabled="disabled"
    clearable
    :value="value"
    :rules="rules"
    :hide-details="!rules.length"
    @input="onChange"
  />
</template>

<script>

export default {
  name: 'TextSearch2',
  props: {
    rules: {
      type: Array,
      default() {
        return [];
      },
    },
    value: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      debounceTime: 1500,
      filtering: {
        search: null,
      },
    };
  },
  methods: {
    onChange(searchText) {
      this.$emit('input', searchText);
    },
  },
};
</script>
