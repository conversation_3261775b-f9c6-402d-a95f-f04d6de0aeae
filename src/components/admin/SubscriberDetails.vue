<template>
  <div>
    <v-tabs
      v-model="tabs"
      dens
      dark
      background-color="secondary lighten-1"
    >
      <v-tab
        key="invoice-company-data-edit"
        ripple
      >
        {{ $t('common_companyData') }}
      </v-tab>
      <v-tab
        key="invoice-company-logo-edit"
        ripple
      >
        {{ $t('common_invoice_logo') }}
      </v-tab>
      <v-tab
        v-if="hasRole('ROLE_SUPERADMIN')"
        key="invoice-company-details"
        ripple
      >
        {{ $t('admin_detailsButton') }}
      </v-tab>
      <v-tab
        key="subscriber-users-list"
        ripple
      >
        {{ $t('common_users') }}
      </v-tab>
      <v-tab
        key="invoice-company-subscriptions-list"
        ripple
      >
        {{ $t('common_subscription') }}
      </v-tab>
      <v-tab
        key="invoice-company-carwashes"
        ripple
      >
        {{ $t('common_administrationCarwashes') }}
      </v-tab>
      <v-tab
        key="subscriber-reports"
        ripple
      >
        {{ $t('common_reports') }}
      </v-tab>
    </v-tabs>
    <div
      class="pa-4"
    >
      <alerts-list
        :disable-links="true"
        :alerts="alerts"
      />
      <v-tabs-items
        v-model="tabs"
      >
        <v-tab-item
          key="invoice-company-data-edit"
        >
          <invoice-company-data-edit
            :subscriber-id="parseInt(subscriberId, 10)"
            :is-editable="true"
            :show-notice="false"
            data-url="/administration/subscriber"
            @data-changed="invoiceDataChanged"
          />
        </v-tab-item>
        <v-tab-item
          key="invoice-company-logo-edit"
        >
          <logo-edit
            :subscriber-id="parseInt(subscriberId, 10)"
            :is-editable="true"
            data-url="/administration/subscriber"
          />
        </v-tab-item>
        <v-tab-item
          v-if="hasRole('ROLE_SUPERADMIN')"
          key="invoice-company-details"
        >
          <details-list
            :subscriber-id="subscriberId"
          />
        </v-tab-item>
        <v-tab-item
          key="subscriber-users-list"
        >
          <subscriber-users-list
            :subscriber-id="parseInt(subscriberId, 10)"
          />
        </v-tab-item>
        <v-tab-item
          key="invoice-company-subscriptions-list"
        >
          <subscriber-subscription-tab
            :subscriber-id="subscriberId"
          />
        </v-tab-item>
        <v-tab-item
          key="invoice-company-carwashes"
        >
          <carwashes-list
            :subscriber-id="subscriberId"
          />
        </v-tab-item>
        <v-tab-item
          key="subscriber-reports"
        >
          <subscriber-reports
            :subscriber-id="subscriberId"
          />
        </v-tab-item>
      </v-tabs-items>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import AlertsList from '@components/common/AlertsList.vue';
import InvoiceCompanyDataEdit from '@components/user/InvoiceCompanyDataEdit.vue';
import LogoEdit from '@components/user/LogoEdit.vue';
import SubscriberSubscriptionTab from '@components/subscription/SubscriberSubscriptionTab.vue';
import SubscriberReports from './subscriber-reports/SubscriberReports.vue';
import SubscriberUsersList from './subscriber/UserList.vue';
import CarwashesList from './subscriber/CarwashesList.vue';
import DetailsList from './subscriber/DetailsList.vue';

export default {
  name: 'SubscriberDetails',
  components: {
    AlertsList,
    SubscriberUsersList,
    InvoiceCompanyDataEdit,
    SubscriberSubscriptionTab,
    CarwashesList,
    DetailsList,
    LogoEdit,
    SubscriberReports,
  },
  props: {
    subscriberId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      tabs: null,
      invoiceData: {
        info: null,
      },
    };
  },
  computed: {
    alerts() {
      return this.invoiceData.info ?? [];
    },
    ...mapGetters({
      hasRole: 'auth/hasRole',
    }),
  },
  methods: {
    invoiceDataChanged(data) {
      this.invoiceData = data;
    },
  },
};
</script>
