<template>
  <v-tooltip bottom>
    <template #activator="{ on, attrs }">
      <v-icon
        :color="statusInfo.color"
        v-bind="attrs"
        v-on="on"
      >
        {{ statusInfo.icon }}
      </v-icon>
    </template>
    <span> {{ statusInfo.text }}</span>
  </v-tooltip>
</template>

<script>

/**
 * ikonka statusu faktury
 * zgodnie z https://gitlab.bkf.pl/bkf/ebkf/carwashmanager/library/invoices/-/blob/main/src/Enum/PaymentStatus.php
 */
export default {
  name: 'InvoiceStatus',
  props: {
    status: {
      type: String,
      required: true,
    },
  },
  computed: {
    statusInfo() {
      const STATUS_MAP = {
        paid: {
          icon: 'mdi-check-circle-outline',
          color: 'green darken-2',
          text: 'common_confirmed',
        },
        pending: {
          icon: 'mdi-progress-clock',
          color: 'progress',
          text: 'common_waiting',
        },
        cancelled: {
          icon: 'mdi-close-circle-outline',
          color: 'error',
          text: 'common_canceled',
        },
      };

      const status = STATUS_MAP[this.status];
      if (status) {
        return {
          icon: status.icon,
          color: status.color,
          text: this.$t(status.text),
        };
      }

      return {
        icon: 'mdi-help-circle',
        color: 'grey',
        text: this.$t('invoice.status.unknown'),
      };
    },
  },
};
</script>
