<template>
  <div>
    <v-row>
      <v-col>
        <text-search
          v-model="search"
        />
      </v-col>
      <v-col>
        <multiselect
          v-model="invoiceExists"
          unified
          :items="topUpInvoiceExists"

          prepend-icon="mdi-file-outline"
          :label="$t('common_invoice')"
        />
      </v-col>
      <v-col>
        <date-select
          :show-custom="false"
          @change="onDateRangeChange"
        />
      </v-col>
    </v-row>
    <v-row class="mt-0">
      <v-col
        class="py-0"
      >
        <multiselect
          v-model="status"
          :items="allowedStatuses"
          prepend-icon="mdi-state-machine"
          :label="$t('common_state')"
        />
      </v-col>
      <v-col
        class="py-0"
      >
        <multiselect
          v-model="sources"
          :items="sourceOptions"
          :label="$t('loyaltyCards_source')"
          :return-array="true"
          prepend-icon="mdi-point-of-sale"
          unified
          allow-null
        />
      </v-col>
      <v-col
        class="py-0"
      >
        <multiselect
          v-model="types"
          :items="allowedTypes"
          :label="$t('fiscal_transactions.table.type')"
          :return-array="true"
          prepend-icon="mdi-cash-multiple"
          unified
          allow-null
        />
      </v-col>
    </v-row>
  </div>
</template>
<script>
import TextSearch from '@components/common/filters/TextSearch.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';
import DateSelect from '@components/common/page/selects/DateSelect.vue';
import debounce from 'lodash/debounce';

import SourceType from '@components/libs/standard-types/types';

import { TransactionType, TopUpProgressType } from '@components/loyalty-cards/types';

export default {

  components: {
    DateSelect,
    TextSearch,
    Multiselect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      search: this.param.search ?? null,
      invoiceExists: this.param.invoiceExists ?? null,
      sources: this.param.sources?.join(',') ?? null,
      status: this.param.status?.join(',') ?? null,
      types: this.param.types?.join(',') ?? null,
      interval: this.param.interval ?? null,
    };
  },
  computed: {
    // scalony obiekt filtrów do przekazania
    internalParam() {
      return {
        search: this.search,
        invoiceExists: this.invoiceExists,
        sources: this.sources,
        status: this.status,
        types: this.types,
        interval: this.interval,
        report: 'v2\\LoyaltyTopUps3Report',
      };
    },
    allowedTypes() {
      return TransactionType.map((item) => ({
        text: this.$t(item.text),
        value: item.value,
        icon: item.icon,
      }));
    },
    allowedStatuses() {
      return TopUpProgressType.map((item) => ({
        text: this.$t(item.text),
        value: item.value,
        icon: item.icon,
      }));
    },
    sourceOptions() {
      const keys = ['INTERNET', 'MONEY_CHANGER'];
      return SourceType
        .filter((option) => keys.includes(option.value))
        .map((item) => ({
          text: this.$t(item.text),
          value: item.value,
          icon: item.icon,
        }));
    },
    topUpInvoiceExists() {
      return [
        {
          text: this.$t('loyaltyCards_withInvoice'),
          value: 1,
          icon: 'mdi-file-outline',
        },
        {
          text: this.$t('loyaltyCards_withoutInvoice'),
          value: 0,
          icon: 'mdi-file-remove-outline',
        },
      ];
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
  },
  beforeCreate() {
    // jeden debounced watcher na cały internalParam
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },

  methods: {
    onDateRangeChange(dateRange) {
      this.interval = dateRange.value;
    },
  },
};
</script>
