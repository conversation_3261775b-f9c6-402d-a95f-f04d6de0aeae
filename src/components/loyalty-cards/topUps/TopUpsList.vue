<template>
  <report-data-table
    ref="dataTable"
    :title="$t('loyaltyCards_loyaltyTopupsHistoryHeading')"
    :headers="headers"
    url="/api/loyalty/v3/top_ups"
    :filters="params"
  >
    <template #[`table-actions`]>
      <card-multiple-add-modal @reload-card-list="fetchData" />
    </template>

    <template #[`item.cardNumber`]="{ item }">
      <card-number :number="item.cardNumber" />
    </template>

    <template #[`item.client`]="{ item }">
      <client-name :name="item.cardClientName" />
    </template>

    <template #[`item.status`]="{ item }">
      <v-row>
        <top-up-progress-badge :status="item.progress" />
        <device-type-badge
          :source="item.source"
        />
      </v-row>
    </template>

    <template #[`item.type`]="{ item }">
      <transaction-type-badge :status="item.type" />
    </template>

    <template #[`item.topUpToSend`]="{ item }">
      <value-currency
        :value="item.topUpToSend"
        :symbol="item.currencySymbol"
      />
    </template>

    <template #[`item.topUpSent`]="{ item }">
      <value-currency
        :value="item.topUpSent"
        :symbol="item.currencySymbol"
      />
    </template>
    <template #[`item.topUpValue`]="{ item }">
      <value-currency
        :value="item.topUpValue"
        :symbol="item.currencySymbol"
      />
    </template>

    <template #[`item.invoice`]="{ item }">
      <top-up-generate-invoice-modal
        v-if="item.invoiceGenerate"
        :id="item.id"
        :client-id="item.cardClientId"
        @generateSuccess="fetchData"
      />
      <div v-else>
        {{ item.invoiceNumber ?? '-' }}
      </div>
    </template>
  </report-data-table>
</template>

<script>

import ReportDataTable from '@components/reports/ReportDataTable.vue';
import CardMultipleAddModal from '@components/loyalty-cards/topUps/CardMultipleAddModal.vue';
import TopUpProgressBadge from '@components/loyalty-cards/badge/TopUpProgressBadge.vue';
import DeviceTypeBadge from '@components/libs/standard-types/badge/DeviceTypeBadge.vue';
import TransactionTypeBadge from '@components/loyalty-cards/badge/TransactionTypeBadge.vue';
import ValueCurrency from '@components/common/formatters/CurrencyFormatter.vue';
import TopUpGenerateInvoiceModal
  from '@components/loyalty-cards/topUps/TopUpGenerateInvoiceModal.vue';
import CardNumber from '@components/loyalty-cards/badge/CardNumber.vue';
import ClientName from '@components/loyalty-cards/badge/ClientName.vue';

export default {
  components: {
    ClientName,
    CardNumber,
    TopUpGenerateInvoiceModal,
    ValueCurrency,
    TransactionTypeBadge,
    DeviceTypeBadge,
    TopUpProgressBadge,
    CardMultipleAddModal,
    ReportDataTable,
  },
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('common_tableDate'),
          value: 'ctime',
          showInRowExpand: true,
          displayMethod: 'date',
          sortable: false,
        },
        {
          text: this.$t('loyaltyCards_number'),
          value: 'cardNumber',
          class: 'md-and-up',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('loyaltyCards_name'),
          value: 'cardAlias',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('common_client'),
          value: 'client',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('loyaltyCards_sourceStatus'),
          value: 'status',
        },
        {
          text: this.$t('fiscal_transactions.table.type'),
          value: 'type',
        },
        {
          text: this.$t('common_topupSent'),
          value: 'topUpSent',
          align: 'right',
        },
        {
          text: this.$t('loyaltyCards_topUpsToSent'),
          value: 'topUpToSend',
          align: 'right',
        },
        {
          text: this.$t('loyaltyCards_topup'),
          value: 'topUpValue',
          align: 'right',
        },
        {
          text: this.$t('loyaltyCards_addedBy'),
          value: 'addedBy',
          align: 'right',
        },
        {
          text: this.$t('common_invoice'),
          value: 'invoice',
          align: 'center',
          showInRowExpand: true,
          sortable: false,
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};
</script>
