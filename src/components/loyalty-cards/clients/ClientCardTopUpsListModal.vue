<template>
  <v-row justify="center">
    <v-dialog
      v-model="dialog"
      fullscreen
      hide-overlay
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('common_topups') }} - {{ clientName }}
            </h5>
          </span>
          <v-spacer />
          <v-btn
            icon
            text
            tile
            small
            dark
            @click="closeDialog"
          >
            <v-icon>
              mdi-close
            </v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-container fluid>
            <v-row>
              <v-col
                cols="12"
                class="text-sm-start pt-6 pb-2"
              >
                <h2>
                  <span>{{ $t('loyaltyCards_loyaltyTopupsHistoryHeading') }}</span>
                </h2>
              </v-col>
            </v-row>
            <v-row class="align-center mt-0">
              <v-col
                cols="12"
                sm="4"
                md="3"
                class="d-flex align-center"
              >
                <text-search
                  v-model="search"
                  :placeholder="$t('common_search')"
                  class="mb-0"
                  @click:clear="search = null"
                />
              </v-col>
              <v-col
                cols="12"
                sm="4"
                md="3"
              >
                <date-range-picker
                  key="dateRange"
                  ref="dateRange"
                  prepend-icon="mdi-calendar-range"
                  :show-presets="true"
                  :show-custom="true"
                  :disabled="loader"
                  start-preset="currentMonth"
                  @reload-transaction-list="onDateRangeChange"
                />
              </v-col>
              <v-col
                cols="12"
                sm="12"
                md="6"
                class="d-flex justify-end align-center"
              >
                <btn-refresh
                  class="mr-2"
                  @click="getData"
                />
                <report-create-modal
                  btn-class="ml-2"
                  :disabled="loader"
                  :params="exportAsyncParams"
                  :preset="null"
                />
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12">
                <v-data-table
                  key="loyaltyTransactionsTable"
                  mobile-breakpoint="0"
                  :headers="dataTable.headers"
                  :items="dataTable.items"
                  item-key="id"
                  :loading="loader"
                  :options.sync="pagination"
                  :server-items-length="dataTable.totalItems"
                  :footer-props="dataTable.footerProps"
                >
                  <template #progress>
                    <div class="text-center mx-n4">
                      <v-progress-linear
                        class="loader"
                        indeterminate
                        color="primary"
                      />
                    </div>
                  </template>
                  <template #item="{ item }">
                    <template v-if="!loader">
                      <tr>
                        <td class="text-sm-start">
                          <span v-if="item.ctime">
                            {{ item.ctime|formatDateDayTime }}
                          </span>
                          <span v-else>-</span>
                        </td>
                        <td class="text-sm-start">
                          {{ item.cardNumber }}
                        </td>
                        <td
                          class="hidden-sm-and-down md-and-up text-sm-start"
                        >
                          {{ item.cardAlias || '–' }}
                        </td>
                        <td class="hidden-sm-and-down md-and-up text-sm-start">
                          <span v-if="item.cardClientId">{{ item.cardClientName }}</span>
                          <span v-else>–</span>
                        </td>
                        <td class="text-sm-start">
                          <div class="d-flex justify-end">
                            <progress-badge
                              :value="item.progress"
                              :tooltip="true"
                            />
                            <device-type-badge
                              :source="item.source"
                            />
                          </div>
                        </td>
                        <td class="text-sm-start border-right">
                          <v-tooltip bottom>
                            <template #activator="{ on, attrs }">
                              <v-icon
                                v-bind="attrs"
                                v-on="on"
                              >
                                {{ getPaymentTypeDetails(item.type).icon }}
                              </v-icon>
                            </template>
                            <span>{{ getPaymentTypeDetails(item.type).text }}</span>
                          </v-tooltip>
                        </td>
                        <td class="text-end hidden-sm-and-down md-and-up">
                          {{ item.topUpSent|currencySymbol(item.currencySymbol) }}
                        </td>
                        <td class="text-end hidden-sm-and-down md-and-up">
                          {{ item.topUpToSend|currencySymbol(item.currencySymbol) }}
                        </td>
                        <td class="text-end border-right">
                          {{ item.topUpValue|currencySymbol(item.currencySymbol) }}
                        </td>
                        <td class="hidden-sm-and-down md-and-up text-sm-center">
                          <template v-if="item.addedBy">
                            <v-tooltip bottom>
                              <template #activator="{ on, attrs }">
                                <v-icon
                                  v-bind="attrs"
                                  v-on="on"
                                >
                                  mdi-account
                                </v-icon>
                              </template>
                              <span>
                                {{ $t('loyaltyCards_addedBy') }} {{ item.addedBy }}
                              </span>
                            </v-tooltip>
                          </template>
                          <template v-else>
                            –
                          </template>
                        </td>
                        <td class="text-end">
                          {{ item.invoiceNumber ?? '-' }}
                        </td>
                        <td>
                          <v-col
                            class="d-flex justify-end pt-1 pb-1"
                          >
                            <template v-if="item.invoice">
                              <act-download
                                :url="`/api/gateway/bkfpay-owner/invoice/${item.invoice}/download`"
                              />
                              <top-up-send-invoice-modal
                                v-if="item.cardClientId !== null"
                                :invoice-id="item.invoice"
                                x-small
                                :client-id="item.cardClientId"
                              />
                            </template>
                            <top-up-generate-invoice-modal
                              v-else-if="item.invoiceGenerate"
                              :id="item.id"
                              :client-id="item.cardClientId"
                              @generateSuccess="getData"
                            />
                          </v-col>
                        </td>
                      </tr>
                    </template>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>
    <add-client-card-modal
      ref="addCardModal"
      :client-id="clientId"
      @reload-card-list="getData"
    />
  </v-row>
</template>

<script>
import TextSearch from '@components/common/filters/TextSearch.vue';
import { debounce } from 'lodash';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import TransactionTypeMixin from '@components/common/badge/icon-text-mixin/TransactionTypeMixin.vue';
import ProgressMixin from '@components/common/badge/icon-text-mixin/ProgressMixin.vue';
import ProgressBadge from '@components/common/badge/ProgressBadge.vue';
import DeviceTypeBadge from '@components/libs/standard-types/badge/DeviceTypeBadge.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import DeviceTypeMixin from '@components/common/badge/icon-text-mixin/DeviceTypeMixin.vue';
import TopUpGenerateInvoiceModal from '@components/loyalty-cards/topUps/TopUpGenerateInvoiceModal.vue';
import TopUpSendInvoiceModal from '@components/loyalty-cards/topUps/TopUpSendInvoiceModal.vue';
import ActDownload from '@components/common/Action/ActDownload.vue';
import AddClientCardModal from './AddClientCardModal.vue';
import BtnRefresh from '../../common/button/BtnRefresh.vue';

export default {
  name: 'ClientCardTopUpsListModal',
  components: {
    ActDownload,
    BtnRefresh,
    AddClientCardModal,
    ProgressBadge,
    TextSearch,
    ReportCreateModal,
    DateRangePicker,
    DeviceTypeBadge,
    TopUpGenerateInvoiceModal,
    TopUpSendInvoiceModal,
  },
  mixins: [
    DeviceTypeMixin,
    ExportMixin,
    ProgressMixin,
    SnackbarMixin,
    TransactionTypeMixin,
  ],
  props: {
    clientId: {
      type: [Number],
      required: true,
    },
    clientName: {
      type: [String],
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loader: false,
      pagination: {
        page: 1,
        itemsPerPage: 10,
        sortBy: ['createdAt'],
        sortDesc: [true],
      },
      dataTable: {
        footerProps: {
          'items-per-page-options': [10, 25, 50],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        headers: [
          {
            text: this.$t('common_tableDate'),
            value: 'createdAt',
            showInRowExpand: true,
            displayMethod: 'date',
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_number'),
            value: 'cardNumber',
            class: 'md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_name'),
            value: 'cardName',
            class: 'hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_client'),
            value: 'cardClientName',
            class: 'hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_sourceStatus'),
            value: 'progress',
            class: 'md-and-up',
            displayMethod: 'top-up-progress',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('fiscal_transactions.table.type'),
            value: 'type',
            class: 'md-and-up',
            displayMethod: 'top-up-progress',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_topupSent'),
            value: 'topUpToSend',
            class: 'hidden-sm-and-down md-and-up text-end',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_topUpsToSent'),
            value: 'topUpSent',
            class: 'hidden-sm-and-down md-and-up text-end',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_topup'),
            value: 'topUpValue',
            class: 'text-end',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_addedBy'),
            value: 'addedBy',
            class: 'hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_invoiceNumber'),
            value: 'invoiceNumber',
            align: 'end',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_invoice'),
            value: 'invoice',
            class: 'text-end',
            showInRowExpand: true,
            sortable: false,
          },
        ],
        items: [],
        totalItems: 0,
      },
      search: '',
      dateRange: {
        from: null,
        to: null,
      },
    };
  },
  computed: {
    exportAsyncParams() {
      return {
        report: 'v2\\LoyaltyTopUps3Report',
        startDate: this.dateRange.from
          ? this.$options.filters.formatDateDay(this.dateRange.from)
          : null,
        endDate: this.dateRange.to ? this.$options.filters.formatDateDay(this.dateRange.to) : null,
        search: this.search || null,
        clientId: this.clientId,
      };
    },
  },
  watch: {
    dialog(val) {
      if (val) {
        if (this.$refs.dateRange != null) {
          this.$refs.dateRange.resetPreset();
        }
      } else {
        this.search = '';
        this.pagination.page = 1;
        this.pagination.itemsPerPage = 10;
        this.dataTable.items = [];
      }
    },
    pagination: {
      handler(newValue, oldValue) {
        if (
          oldValue.page !== newValue.page
          || oldValue.itemsPerPage !== newValue.itemsPerPage
        ) {
          this.getDataDebounced();
        }
      },
      deep: true,
    },
    search() {
      this.pagination.page = 1;
      this.getDataDebounced();
    },
  },
  created() {
    this.getDataDebounced = debounce(this.getData, 300);
  },

  methods: {
    async getData() {
      this.loader = true;
      this.dataTable.items = []; // Clear items before fetching new ones
      try {
        const params = {
          page: this.pagination.page,
          perPage: this.pagination.itemsPerPage,
          clientId: this.clientId,
          search: this.search || null,
        };

        if (this.dateRange.from && this.dateRange.to) {
          params.startDate = this.$options.filters.formatDateDay(this.dateRange.from);
          params.endDate = this.$options.filters.formatDateDay(this.dateRange.to);
        }

        const response = await this.axios.get('/api/loyalty/v3/top_ups', {
          params,
        });
        if (response.status === 200 && response.data) {
          // Create new array reference to trigger reactivity
          this.dataTable.items = [...(response.data.data ?? [])];
          this.dataTable.totalItems = response.data.total ?? 0;
        }
      } catch (error) {
        this.dataTable.items = [];
        this.dataTable.totalItems = 0;
      } finally {
        this.loader = false;
      }
    },
    show() {
      this.dialog = true;
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    onDateRangeChange(dates) {
      this.dateRange = dates;
      this.getData();
    },
    closeDialog() {
      this.dialog = false;
    },
  },
};
</script>

<style lang="css" scoped>
.card-container {
  display: flex;
  align-items: center;
}

.card-number {
  font-weight: bold;
}
</style>
