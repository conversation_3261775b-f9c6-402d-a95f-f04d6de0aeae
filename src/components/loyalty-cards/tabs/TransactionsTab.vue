<template>
  <div>
    <transactions-filter v-model="filters" />
    <transactions-list
      ref="dataTable"
      :params="filters"
    />
  </div>
</template>

<script>

import TransactionsFilter from '@components/loyalty-cards/transactions/TransactionsFilter.vue';
import TransactionsList from '@components/loyalty-cards/transactions/TransactionsList.vue';

export default {
  components: {
    TransactionsList,
    TransactionsFilter,
  },
  data() {
    return {
      loading: false,
      filters: {},
    };
  },
  watch: {
    filters: {
      handler(newVal) {
        localStorage.setItem('bkfpay-transactions-filter', JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  created() {
    const savedFilter = localStorage.getItem('bkfpay-transactions-filter');
    if (savedFilter) {
      this.filters = JSON.parse(savedFilter);
    }
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};
</script>

<style lang="css" scoped>
.loader {
  top: 35%;
  z-index: 5;
}

.loader-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
  z-index: 4;
}

</style>
