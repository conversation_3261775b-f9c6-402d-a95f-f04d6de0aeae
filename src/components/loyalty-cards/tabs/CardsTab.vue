<template>
  <div>
    <cards-filter v-model="filters" />
    <cards-list
      ref="dataTable"
      :params="filters"
    />
  </div>
</template>

<script>
import CardsFilter from '@components/loyalty-cards/cards/CardsFilter.vue';
import CardsList from '@components/loyalty-cards/cards/CardsList.vue';

export default {
  components: {
    CardsFilter,
    CardsList,
  },

  data() {
    return {
      loading: false,
      filters: {},
    };
  },
  watch: {
    filters: {
      handler(newVal) {
        localStorage.setItem('bkfpay-cards-filter', JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  created() {
    const savedFilter = localStorage.getItem('bkfpay-cards-filter');
    if (savedFilter) {
      this.filters = JSON.parse(savedFilter);
    }
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};

</script>
