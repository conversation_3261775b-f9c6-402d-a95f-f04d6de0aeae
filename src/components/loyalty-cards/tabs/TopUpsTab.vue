<template>
  <div>
    <top-ups-filter v-model="params" />
    <top-ups-list
      ref="dataTable"
      :params="params"
    />
  </div>
</template>

<script>
import TopUpsFilter from '@components/loyalty-cards/topUps/TopUpsFilter.vue';
import TopUpsList from '@components/loyalty-cards/topUps/TopUpsList.vue';

export default {
  components: {
    TopUpsList,
    TopUpsFilter,
  },
  data() {
    return {
      loading: false,
      params: {},
    };
  },
  watch: {
    params: {
      handler(newVal) {
        localStorage.setItem('bkfpay-topups-filter', JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  created() {
    const savedFilter = localStorage.getItem('bkfpay-topups-filter');
    if (savedFilter) {
      this.params = JSON.parse(savedFilter);
    }
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};
</script>

<style lang="css" scoped>
.loader {
  top: 35%;
  z-index: 5;
}

.loader-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
  z-index: 4;
}

</style>
