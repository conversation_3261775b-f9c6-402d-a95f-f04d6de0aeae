export const TopUpProgressType = [
  {
    value: 'WAITING',
    icon: 'mdi-timer-sand-empty',
    text: 'common_toSent',
  },
  {
    value: 'NOT_FULLY_REFILLED',
    icon: 'mdi-autorenew',
    text: 'common_notFullySent',
  },
  {
    value: 'REFILLED',
    icon: 'mdi-check-underline',
    text: 'common_sendToCard',
  },
  {
    value: 'CANCELED',
    icon: 'mdi-cancel',
    text: 'common_canceled',
  },
];

export const TransactionType = [
  {
    value: 'SUBTRACTION',
    icon: 'mdi-trending-down',
    text: 'transactions.payment',
  },
  {
    value: 'ADDITION',
    icon: 'mdi-trending-up',
    text: 'transactions.topup',
  },
  {
    value: 'ALIGNMENT',
    icon: 'mdi-wrench',
    text: 'transactions.balance_adjustment',
  },
  {
    value: 'PROMOTION',
    icon: 'mdi-sale',
    text: 'transactions.promotions',
  },
  {
    value: 'BLOCKADE',
    icon: 'mdi-lock',
    text: 'loyaltyCards_lockFund',
  },
];

export const CardTypeType = [
  {
    icon: 'mdi-web',
    text: 'loyaltyCards_virtual',
    value: 'VIRTUAL',
  },
  {
    value: 'MIFARE',
    icon: 'mdi-credit-card-outline',
    text: 'loyaltyCards_regular',
  },
];

export const InvoiceStatusType = [
  {
    value: 'paid',
    icon: 'mdi-check-circle-outline',
    color: 'green darken-2',
    text: 'common_confirmed',
  },
  {
    value: 'pending',
    icon: 'mdi-progress-clock',
    color: 'progress',
    text: 'common_waiting',
  },
  {
    value: 'canceled',
    icon: 'mdi-close-circle-outline',
    color: 'error',
    text: 'common_canceled',
  },
];
