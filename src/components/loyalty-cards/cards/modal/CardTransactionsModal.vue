<template>
  <div>
    <btn-history
      :text="$t('transactions.history_for_card')"
      @click="openDialog"
    />
    <v-dialog
      v-model="dialog"
      fullscreen
      hide-overlay
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ cardTitle }}
            </h5>
          </span>
          <v-spacer />
          <v-btn
            icon
            text
            tile
            small
            dark
            @click="dialog = false"
          >
            <v-icon>
              mdi-close
            </v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
            <transactions-list
              :params="{
                cardToken: cardToken
              }"
            />
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>

import BtnHistory from '@components/common/button/BtnHistory.vue';
import TransactionsList from '@components/loyalty-cards/transactions/TransactionsList.vue';

export default {
  components: {
    TransactionsList,
    BtnHistory,
  },
  props: {
    cardToken: {
      type: String,
      default: null,
    },
    cardName: {
      type: String,
      default: null,
    },
    cardNumber: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      dialog: false,
    };
  },
  computed: {
    cardTitle() {
      return `${this.$t('transactions.history_for_card')} - ${this.cardNumber} (${this.cardName})`;
    },
  },
  methods: {
    openDialog() {
      this.dialog = true;
    },
  },
};
</script>
