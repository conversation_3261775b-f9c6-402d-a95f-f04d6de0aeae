<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search v-model="search" />
      </v-col>
      <v-col>
        <multiselect
          v-model="virtual"
          :items="filters.virtual.options"
          :label="$t('loyaltyCards_cardType')"
          prepend-icon="mdi-web"
          unified
          allow-null
        />
      </v-col>
      <v-col class="d-flex align-center justify-end">
        <v-row>
          <v-switch
            v-model="usedCards"
            hide-details
            class="mr-2"
          />
          <date-select
            :disabled="!usedCards"
            :show-custom="false"
            @change="onDateRangeChange"
          />
        </v-row>
      </v-col>
    </v-row>

    <v-row class="mt-0">
      <v-col class="py-0">
        <multiselect
          v-model="status"
          :items="filters.status.options"
          :label="$t('loyaltyCards_activity')"
          prepend-icon="mdi-credit-card-settings-outline"
          unified
          allow-null
        />
      </v-col>
      <v-col class="py-0">
        <multiselect
          v-model="foundsExists"
          :items="filters.founds.options"
          :label="$t('loyaltyCards_funds')"
          prepend-icon="mdi-wallet"
          unified
          allow-null
        />
      </v-col>
      <v-col class="py-0">
        <multiselect
          v-model="nameExists"
          :items="filters.name.options"
          :label="$t('loyaltyCards_names')"
          prepend-icon="mdi-format-text"
          unified
          allow-null
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import TextSearch from '@components/common/filters/TextSearch.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';
import DateSelect from '@components/common/page/selects/DateSelect.vue';
import debounce from 'lodash/debounce';

export default {
  components: {
    DateSelect,
    TextSearch,
    Multiselect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      search: this.param.search,
      virtual: this.param.virtual,
      status: this.param.status,
      usedCards: this.param.usedCards,
      nameExists: this.param.nameExists,
      foundsExists: this.param.foundsExists,
      interval: this.param.interval,
      filters: {
        name: {
          options: [
            { text: this.$t('loyaltyCards_withNames'), value: 1, icon: 'mdi-format-title' },
            { text: this.$t('loyaltyCards_withoutNames'), value: 0, icon: 'mdi-format-strikethrough' },
          ],
        },
        virtual: {
          options: [
            { text: this.$t('loyaltyCards_virtual'), value: 1, icon: 'mdi-web' },
            { text: this.$t('loyaltyCards_regular'), value: 0, icon: 'mdi-credit-card-outline' },
          ],
        },
        status: {
          options: [
            { text: this.$t('loyaltyCards_filtersActive'), value: 'ACTIVE', icon: 'mdi-lock-open' },
            { text: this.$t('loyaltyCards_blocked'), value: 'LOCKED', icon: 'mdi-lock' },
          ],
        },
        founds: {
          options: [
            { text: this.$t('loyaltyCards_withFounds'), value: 1, icon: 'mdi-currency-usd' },
            { text: this.$t('loyaltyCards_withoutFounds'), value: 0, icon: 'mdi-currency-usd-off' },
          ],
        },
      },
    };
  },
  computed: {
    // scalony obiekt filtrów do przekazania
    internalParam() {
      return {
        search: this.search,
        virtual: this.virtual?.join(',') ?? null,
        status: this.status?.join(',') ?? null,
        nameExists: this.nameExists?.join(',') ?? null,
        foundsExists: this.foundsExists?.join(',') ?? null,
        interval: this.usedCards ? this.interval : null,
        report: 'v2\\LoyaltyCards3Report',
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
  },
  beforeCreate() {
    // jeden debounced watcher na cały internalParam
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },

  methods: {
    onDateRangeChange(dateRange) {
      this.interval = dateRange.value;
    },
  },
};
</script>
