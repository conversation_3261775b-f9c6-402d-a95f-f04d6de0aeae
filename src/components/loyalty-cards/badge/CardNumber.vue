<template>
  <v-tooltip
    v-if="status === 'BLOCKED'"
    bottom
  >
    <template #activator="{ on, attrs }">
      <div
        class="notice-icon"
        v-bind="attrs"
        style="color: red; text-decoration: line-through;"
        v-on="on"
      >
        {{ number.toUpperCase() }}
      </div>
    </template>
    <span>{{ $t('common_cardBlocked') }}</span>
  </v-tooltip>
  <div v-else>
    {{ number.toUpperCase() }}
  </div>
</template>

<script>

export default {
  components: { },
  props: {
    number: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      default: null,
    },
  },

};
</script>
