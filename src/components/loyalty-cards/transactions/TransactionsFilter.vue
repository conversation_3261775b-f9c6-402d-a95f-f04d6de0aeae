<template>
  <div>
    <v-row>
      <v-col>
        <text-search
          v-model="search"
        />
      </v-col>

      <v-col>
        <multi-select
          v-model="cardType"
          :items="CardTypeOptions"
          :label="$t('loyaltyCards_cardType')"
          prepend-icon="mdi-web"
        />
      </v-col>

      <v-col>
        <carwash-select
          v-model="carWashSerialNumber"
        />
      </v-col>
      <v-col>
        <date-select
          :show-custom="false"
          @change="onDateRangeChange"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <multi-select
          v-model="type"
          :items="TransactionTypeOptions"
          :label="$t('common_transactionType')"
          prepend-icon="mdi-clipboard-list-outline"
        />
      </v-col>
      <v-col>
        <multi-select
          v-model="nameExists"
          :items="NamesStatusOptions"
          :label="$t('loyaltyCards_names')"
          prepend-icon="mdi-format-text"
        />
      </v-col>
      <v-col>
        <multi-select
          v-model="source"
          :items="sourceOptions"
          :label="$t('loyaltyCards_source')"
          prepend-icon="mdi-car-wash"
        />
      </v-col>
    </v-row>
  </div>
</template>
<script>
import TextSearch from '@components/common/filters/TextSearch.vue';
import debounce from 'lodash/debounce';

import { CardTypeType, TransactionType } from '@components/loyalty-cards/types';
import CarwashSelect from '@components/common/page/selects/CarwashSelect.vue';
import DateSelect from '@components/common/page/selects/DateSelect.vue';
import SourceType from '@components/libs/standard-types/types';
import MultiSelect from '@components/common/filters/Multiselect.vue';

export default {

  components: {
    MultiSelect,
    DateSelect,
    CarwashSelect,
    TextSearch,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      search: this.param.search ?? null,
      cardType: this.param.cardType ?? null,
      carWashSerialNumber: this.param.carWashSerialNumber ?? null,
      nameExists: this.param.nameExists ?? null,
      type: this.param.type ?? null,
      source: this.param.source ?? null,
      interval: this.param.interval ?? null,
    };
  },
  computed: {
    CardTypeOptions() {
      return CardTypeType.map((item) => ({
        text: this.$t(item.text),
        value: item.value,
        icon: item.icon,
      }));
    },
    TransactionTypeOptions() {
      return TransactionType.map((item) => ({
        text: this.$t(item.text),
        value: item.value,
        icon: item.icon,
      }));
    },
    sourceOptions() {
      return SourceType.map((item) => ({
        text: this.$t(item.text),
        value: item.value,
        icon: item.icon,
      }));
    },
    NamesStatusOptions() {
      return [
        {
          text: this.$t('loyaltyCards_withNames'),
          value: 1,
          icon: 'mdi-format-title',
        },
        {
          text: this.$t('loyaltyCards_withoutNames'),
          value: 0,
          icon: 'mdi-format-strikethrough',
        },
      ];
    },
    // scalony obiekt filtrów do przekazania
    internalParam() {
      return {
        search: this.search,
        cardType: this.cardType?.join(',') ?? null,
        carwash: this.carWashSerialNumber?.join(',') ?? null,
        nameExists: this.nameExists?.join(',') ?? null,
        type: this.type?.join(',') ?? null,
        source: this.source?.join(',') ?? null,
        interval: this.interval,
        report: 'v2\\LoyaltyTransactions3Report',
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
  },
  beforeCreate() {
    // jeden debounced watcher na cały internalParam
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },

  methods: {
    onDateRangeChange(dateRange) {
      this.interval = dateRange.value;
    },
  },
};
</script>
