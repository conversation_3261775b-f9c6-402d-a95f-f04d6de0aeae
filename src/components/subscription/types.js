export const StatusesType = {
  paid: {
    text: 'administration.subscription.status.paid',
    color: 'green',
  },
  initiated_proforma: {
    text: 'administration.subscription.status.initiated_proforma',
    color: 'orange',
  },
  manually_canceled: {
    text: 'administration.subscription.status.manually_canceled',
    color: 'red',
  },
  error: {
    text: 'administration.subscription.status.error',
    color: 'red',
  },
  canceled: {
    text: 'administration.subscription.status.canceled',
    color: 'red',
  },
  initiated: {
    text: 'administration.subscription.status.initiated',
    color: 'orange',
  },
};

export const SubscriptionType = {
  UNSUBSCRIBED: {
    text: 'subscription.table.UNSUBSCRIBED',
  },
  WARRANTY: {
    text: 'subscription.table.WARRANTY',
  },
  STANDARD: {
    text: 'subscription.table.STANDARD',
  },
};

export const SubscriptionCarwashType = {
  UNSUBSCRIBED: {
    text: 'subscription.carwash-type.UNSUBSCRIBED',
    color: 'orange',
  },
  WARRANTY: {
    text: 'subscription.carwash-type.WARRANTY',
    color: 'green',
  },
  STANDARD: {
    text: 'subscription.carwash-type.STANDARD',
    color: '',
  },
};
