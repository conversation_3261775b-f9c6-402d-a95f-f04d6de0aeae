<template>
  <v-layout
    row
    justify-center
  >
    <v-dialog
      v-model="dialog"
      content-class="dialogWidth-3"
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('subscriptions.subscription') }} - {{ $t('subscription_whyThisPrice') }}
            </h5>
          </span>
          <v-spacer />
          <v-btn
            small
            fab
            dark
            @click.native="dialog = false"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-container grid-list-md>
            <h3 class="mb-4">
              {{ $t('subscription_whyThisPriceModalHint') }}
            </h3>
            <template v-if="showTable && !loaders.site">
              <subscription-calculation
                :currency-sym="ownerSubscriptionCurrency"
                :subscription-calculation="subscriptionCalculation"
              />
            </template>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="gray"
            :disabled="loaders.submit"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.return_to_list') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<script>

import SubscriptionCalculation from '@components/subscription/SubscriptionCalculation.vue';

export default {
  components: { SubscriptionCalculation },
  props: {
    planId: {
      type: Number,
      default: null,
      required: false,
    },
  },
  data() {
    return {
      subscriptionCalculation: {
        item: {},
        summary: [
          {
            type: 'WARRANTY',
            baseValue: 119,
            discount: 100,
            valueAfterDiscount: 0,
          },
        ],
        summaryAfterDiscount: {
          WARRANTY: 0,
          STANDARD: 0,
        },
        discount: 0,
      },
      dialog: false,
      loaders: {
        submit: false,
        site: false,
      },
      ownerSubscriptionCurrency: undefined,
    };
  },
  computed: {
    showTable() {
      if (this.dialog) {
        this.getPriceCalculation();
        return true;
      }

      return false;
    },
  },
  mounted() {
    this.getPriceCalculation();
  },
  methods: {
    closeDialog() {
      this.loaders.submit = false;
      this.dialog = false;
    },
    getPriceCalculation() {
      if (this.planId !== null && Number.isInteger(this.planId)) {
        this.loaders.site = true;
        this.axios.get(
          `/api/subscriptions/package/${this.planId}/calculate`,
        )
          .then(
            (response) => {
              if (response.status === 200) {
                this.subscriptionCalculation = response.data;
                this.ownerSubscriptionCurrency = response.data.currencySymbol;
                this.loaders.site = false;
              }
            },
            () => {
              // on error
              this.loaders.site = false;
            },
          );
      }
    },
  },
};
</script>
<style lang="stylus">
.bold {
  font-weight: 500;
}

.summary-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px rgba(0, 0, 0, 0.6) solid;
}

.summary-table td {
  padding: 5px;
  border: 1px rgba(0, 0, 0, 0.6) solid;
}

.cmv-summaryRow
  border-top 3px double #8e99aa96

.pl-50
  padding-left 50px !important

.pr-50
  padding-right 50px !important

.clickable
  cursor pointer
</style>
