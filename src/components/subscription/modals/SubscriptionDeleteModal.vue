<template>
  <div>
    <v-tooltip bottom>
      <template #activator="{ on }">
        <span v-on="on">
          <v-btn
            x-small
            :disabled="buttondDisabled(item)"
            tile
            rounded
            fab
            elevation="1"
            color="red"
            class="my-1 white--text"
            @click.stop="dialog = true"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </span>
      </template>
      <span>
        <template
          v-if="item.status === 'canceled' || item.status === 'manually_canceled'"
        >
          {{ $t('admin_alreadyCancel') }}: {{ item.mtime }}
        </template>
        <template v-else-if="item.editable">
          {{ $t('admin_cancel') }}
        </template>
        <template v-else>
          {{ $t('admin_automaticPayment') }}
          <br>
          {{ $t('admin_manualCancelNotPossible') }}
        </template>
      </span>
    </v-tooltip>
    <v-layout
      row
      justify-center
    >
      <v-dialog
        v-model="dialog"
        scrollable
        persisten
        content-class="dialogWidth-3"
        class="modal"
      >
        <v-card>
          <v-card-title class="text-h5">
            {{ $t("subscriptions.delete-question") }}
          </v-card-title>

          <v-card-actions>
            <v-spacer />
            <v-btn
              text
              color="primary"
              @click="dialog = false"
            >
              {{ $t('actions.cancel') }}
            </v-btn>
            <v-btn
              color="red"
              class="white--text text-center"
              @click="cancelSubscriptionAndCLoseDialog()"
            >
              {{ $t('actions.delete') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-layout>
  </div>
</template>

<script>

export default {
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
    onSuccess: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      dialog: false,
    };
  },
  methods: {
    buttondDisabled(item) {
      return item.status === 'canceled'
        || item.status === 'manually_canceled'
        || !item.editable;
    },
    onError() {
      this.closeDialog();
    },
    closeDialog() {
      this.dialog = false;
    },
    cancelSubscriptionAndCLoseDialog() {
      this.cancelSubscription();
      this.dialog = false;
    },
    cancelSubscription() {
      this.loader = true;
      this.axios.delete(
        `/administration/subscription/${this.item.id}`,
      )
        .then(() => {
          this.loader = false;
          this.onSuccess();
        });
    },
  },
};
</script>
