<template>
  <v-layout
    row
    justify-center
  >
    <div class="pt-4 pb-4">
      <v-btn
        :disabled="disabled"
        color="primary"
        @click.native="dialog=true"
      >
        <v-icon class="mr-2">
          mdi-credit-card
        </v-icon>
        {{ $t('subscriptions.actions.chose-and-pay') }}
      </v-btn>
    </div>
    <v-dialog
      v-model="dialog"
      :fullscreen="$vuetify.breakpoint.smAndDown"
      max-width="900px"
      height="100vh"
      transition="dialog-bottom-transition"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('subscription_subscription') }} - {{ stepTitles[step] }}
            </h5>
          </span>
          <v-spacer />
          <v-btn
            small
            fab
            dark
            @click.native="dialog = false"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-progress-linear
          class="no-margin"
          :indeterminate="loading"
        />
        <div
          v-if="loading"
          class="loader-background"
        >
          <v-progress-circular
            size="50"
            class="loader"
            indeterminate
            color="primary"
          />
        </div>
        <template v-if="!loading">
          <v-stepper
            v-model="step"
            class="elevation-0"
          >
            <v-stepper-header class="hidden-sm-and-down">
              <v-stepper-step
                :complete="step > 1"
                step="1"
              >
                {{ stepTitles[1] }}
              </v-stepper-step>
              <v-divider />
              <v-stepper-step
                :complete="step > 2"
                step="2"
              >
                {{ stepTitles[2] }}
              </v-stepper-step>
              <v-divider />
              <v-stepper-step
                :complete="step > 3"
                step="3"
              >
                {{ stepTitles[3] }}
              </v-stepper-step>
              <v-divider />
              <v-stepper-step
                :complete="step > 5"
                step="5"
              >
                {{ stepTitles[5] }}
              </v-stepper-step>
            </v-stepper-header>
            <v-stepper-items>
              <v-stepper-content step="1">
                <div
                  :v-if="Object.keys(subscriptionPlans).length === 0"
                  class="text-sm-center"
                >
                  <h2 class="mb-2">
                    {{ $t('subscription_chose2') }}
                  </h2>
                  <v-layout
                    cols="12"
                    row
                    wrap
                  >
                    <v-col
                      v-for="subType in subscriptionTypes"
                      :key="subType.type"
                      class="mb-2 clickable"
                      cols="12"
                      sm="4"
                    >
                      <v-card
                        class="elevation-0 ml-1 mr-1 pt-2 pb-2 pl-2 pr-2"
                        :class="{
                          'container-active': subscriptionTypeActive[subType.type],
                          'container-inactive': !subscriptionTypeActive[subType.type]
                        }"
                        @click.native="toggleSubscriptionType(subType.type)"
                      >
                        <h2>{{ $t(`subscriptions.types.${subType.type}`) }}</h2>
                        <div
                          class="mt-2 mb-2 clickable"
                          @click="toggleSubscriptionType(subscriptionTypeKey)"
                        />
                        <h2>{{ subType.price }} {{ subType.currencySymbol }}</h2>
                        <h5 class="pt-2">
                          {{ $t('subscription_priceForCarwashForMonth') }}
                        </h5>
                      </v-card>
                    </v-col>
                  </v-layout>

                  <h2 class="mb-2">
                    {{ $t('subscription_subscriptionContent') }}
                  </h2>

                  <v-layout
                    cols="12"
                    row
                    wrap
                  >
                    <v-col
                      v-for="(subscriptionContent, subscriptionContentKey) in subscriptionContents"
                      :key="`context_${subscriptionContentKey}`"
                      class="mb-2"
                      cols="12"
                      sm="4"
                    >
                      <v-card
                        class="elevation-0 ml-1 mr-1 pt-2 pb-2 pl-2 pr-2"
                        :class="{
                          'container-active': subscriptionContentActive[subscriptionContentKey],
                          'container-inactive': !subscriptionContentActive[subscriptionContentKey]
                        }"
                      >
                        <div
                          v-for="(content, key) in subscriptionContent"
                          v-show="content.show"
                          :key="key"
                          class="subscription-content-option"
                        >
                          <v-icon
                            v-if="subscriptionContentActive[subscriptionContentKey]"
                            color="white"
                          >
                            mdi-check
                          </v-icon>
                          <v-icon
                            v-else
                            color="white"
                          >
                            mdi-close
                          </v-icon>
                          {{ content.text }}
                        </div>
                      </v-card>
                    </v-col>
                  </v-layout>

                  <div class="mb-2">
                    <v-btn
                      color="primary"
                      @click="goToPeriodChose()"
                    >
                      {{ $t('subscription_goToAbonamentLength') }}
                    </v-btn>
                  </div>
                </div>
              </v-stepper-content>
              <v-stepper-content step="2">
                <div
                  :v-if="Object.keys(subscriptionPlans).length === 0"
                  class="text-sm-center"
                >
                  <v-layout
                    cols="12"
                    row
                    wrap
                  >
                    <v-col
                      cols="12"
                    >
                      <h2>{{ $t('subscription_chosePaymentPeriod') }}</h2>
                      <h3 class="mb-2">
                        {{ $t('subscription_logerPeriodBetterPrice') }}
                      </h3>
                    </v-col>
                    <v-col
                      v-for="(subscriptionPlansIt, subscriptionLengthIndex)
                        in subscriptionPlans[subscriptionType]"
                      :key="subscriptionLengthIndex"
                      class="mb-2 clickable"
                      cols="12"
                      :sm="columnsWidth"
                    >
                      <v-card
                        class="elevation-0 ml-1 mr-1 pt-2 pb-2 pl-2 pr-2"
                        :class="{
                          'container-active': subscriptionLengthActive[subscriptionLengthIndex],
                          'container-inactive': !subscriptionLengthActive[subscriptionLengthIndex]
                        }"
                        @click.native="toggleSubscriptionLength(
                          subscriptionLengthIndex,
                          subscriptionPlansIt.id
                        )"
                      >
                        <h2>{{ $t(`date.length.${subscriptionLengthIndex}m`) }}</h2>
                        <div>
                          <h3> {{ $t(`subscriptions.types.${subscriptionType}`) }} </h3>
                        </div>
                        <div>
                          <h2>
                            {{ subscriptionPlansIt.value }} {{ subscriptionPlansIt.currencySymbol }}
                          </h2>
                        </div>
                        <div class="mt-2 mb-2" />
                        <h5>
                          {{ $t('subscription_priceForCarwashForMonth') }}
                        </h5>
                      </v-card>
                    </v-col>
                  </v-layout>

                  <h2> {{ $t('subscription_summary') }} </h2>
                  <h1>
                    <template v-if="calculatedSubscription !== null">
                      {{ Number(calculatedSubscription.grossValue).toFixed(2) }}
                      {{ calculatedSubscription.currencySymbol }}
                      <br>
                      <small style="font-size: 0.7em; color: #666;">
                        {{ Number(calculatedSubscription.netValue).toFixed(2) }}
                        {{ calculatedSubscription.currencySymbol }}
                        {{ $t('fiscal_transactions.table.net') }}
                      </small>
                    </template>
                    <template v-else>
                      -
                    </template>
                  </h1>
                  <v-btn
                    text
                    @click.native="openModal('subscriptionPriceSummaryDialog')"
                  >
                    <h4>{{ $t('subscription_whyThisPrice') }}</h4>
                  </v-btn>
                  <div class="mb-2">
                    <v-btn
                      color="primary"
                      @click.native="goToCheckInvoiceData()"
                    >
                      {{ $t('subscription_checkInvoiceData') }}
                    </v-btn>
                  </div>
                  <div class="mb-2">
                    <v-btn
                      text
                      @click.native="stepBack()"
                    >
                      <h4>{{ $t('subscription_back') }}</h4>
                    </v-btn>
                  </div>
                </div>
              </v-stepper-content>
              <v-stepper-content step="3">
                <div
                  :v-if="Object.keys(subscriptionPlans).length === 0"
                  class="text-sm-center"
                >
                  <v-alert
                    text
                    :value="!isDataCompleted"
                    border="left"
                    type="error"
                    class="my-0"
                  >
                    {{ $t('subscription_subscriptionBuyMissingData') }}
                    <a
                      color="error"
                      href="mailto:<EMAIL>"
                    ><EMAIL></a>
                  </v-alert>
                  <invoice-company-data-edit
                    :show-all-fields="false"
                    :is-editable="false"
                    :on-success-submit="checkInvoiceData"
                  />
                  <div class="mb-2">
                    <v-btn
                      color="primary"
                      :disabled="!isDataCompleted"
                      @click.native="initSubscription()"
                    >
                      {{ $t('subscription_orderWithPaymentObligation') }}
                    </v-btn>
                  </div>
                  <div class="mb-2">
                    <v-btn
                      text
                      @click.native="stepBack()"
                    >
                      <h4>{{ $t('subscription_back') }}</h4>
                    </v-btn>
                  </div>
                </div>
              </v-stepper-content>
              <v-stepper-content step="4">
                <div
                  class="text-sm-center mb-12"
                  color="grey lighten-1"
                  height="200px"
                >
                  <v-progress-circular
                    :size="70"
                    :width="7"
                    color="primary"
                    indeterminate
                  />
                  <h1>{{ $t('common_processing') }}</h1>
                </div>
              </v-stepper-content>
              <v-stepper-content step="5">
                <div
                  class="text-sm-center mb-12"
                  color="grey lighten-1"
                  height="200px"
                >
                  <template v-if="paymentData.status=='paid'">
                    <v-icon
                      style="font-size: 76px"
                      color="green darken-2"
                    >
                      mdi-check-circle-outline
                    </v-icon>
                    <h1>{{ $t('common_paid') }}</h1>
                  </template>
                  <template v-else-if="paymentData.status=='canceled'">
                    <v-icon
                      style="font-size: 76px"
                      color="error"
                    >
                      mdi-alert
                    </v-icon>
                    <h1>{{ $t('common_canceled') }}</h1>
                  </template>
                  <template v-else>
                    <v-icon
                      style="font-size: 76px"
                      color="progress"
                    >
                      mdi-cached
                    </v-icon>
                    <h1>{{ $t('common_processing') }}</h1>
                  </template>
                </div>
                <div class="text-sm-center">
                  <v-btn
                    color="primary"
                    @click.native="dialog = false"
                  >
                    {{ $t('actions.close') }}
                  </v-btn>
                </div>
              </v-stepper-content>
              <v-stepper-content step="6">
                <v-alert
                  v-show="errorMessage"
                  class="py-6 my-4"
                  border="left"
                  icon="mdi-information"
                  color="warning"
                >
                  <div class="white--text">
                    {{ errorMessage }}
                  </div>
                </v-alert>
              </v-stepper-content>
            </v-stepper-items>
          </v-stepper>
        </template>
      </v-card>
    </v-dialog>
    <subscription-price-summary-modal
      ref="subscriptionPriceSummaryDialog"
      :plan-id="planId"
    />
  </v-layout>
</template>

<script>
import subscriptionPriceSummaryModal from '@components/subscription/modals/SubscriptionPriceSummaryModal.vue';
import { mapGetters } from 'vuex';
import invoiceCompanyDataEdit from '@components/user/InvoiceCompanyDataEdit.vue';

export default {
  components: {
    invoiceCompanyDataEdit,
    subscriptionPriceSummaryModal,
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      paymentId: this.$route.params.paymentId,
      step: 1,
      isDataCompleted: false,
      topupValue: 10,
      dialog: false,
      errorMessage: null,
      loading: false,
      dataExists: false,
      subscriptionType: 'free',
      subscriptionPlans: {},
      planId: null,
      stepTitles: {
        1: this.$t('subscription_chose'),
        2: this.$t('subscription_chosePaymentPeriod'),
        3: this.$t('subscription_checkInvoiceData'),
        4: this.$t('common_processing'),
        5: this.$t('subscription_paymentSummary'),
        6: '',
      },
      topUpData: {
        confirmed_timestamp: null,
        initiated_timestamp: null,
      },
      paymentData: {},
      subscriptionLength: 1,
      subscriptionTypes: {
        free: {
          type: 'free',
          price: null,
          planId: null,
        },
        basic: {
          type: 'basic',
          price: null,
          planId: null,
        },
        premium: {
          type: 'premium',
          price: null,
          planId: null,
        },
      },
      subscriptionContents: {
        free: [
          {
            text: this.$t('subscription_mobileAppAccess'),
            show: true,
          },
          {
            text: this.$t('subscription_wwwAccess'),
            show: true,
          },
          // {
          //   text: this.$t('subscription_serviceTicketPreview'),
          //   show: true,
          // },
          {
            text: this.$t('subscription_alarmsList'),
            show: true,
          },
          {
            text: this.$t('subscription_technicalData'),
            show: true,
          },
        ],
        basic: [
          {
            text: this.$t('subscription_unlimitedUsers'),
            show: true,
          },
          {
            text: this.$t('subscription_cyclicFiscalMailReports'),
            show: true,
          },
          {
            text: this.$t('subscription_moneycollectControll'),
            show: true,
          },
          {
            text: this.$t('subscription_usageStatistics'),
            show: true,
          },
          {
            text: this.$t('subscription_newAlarmNotifications'),
            show: true,
          },
          {
            text: this.$t('subscription_mailReports'),
            show: true,
          },
          {
            text: this.$t('subscriptions.options.loyalcards-transactions-history'),
            show: true,
          },
        ],
        premium: [
          {
            text: this.$t('subscription_loyalcardsInvoicing'),
            show: true,
          },
          {
            text: this.$t('subscription_loyalcardsReport'),
            show: true,
          },
          {
            text: this.$t('subscription_loyalcardsCyclicTopup'),
            show: true,
          },
          {
            text: this.$t('subscription_loyalcardsTransactionHistory'),
            show: true,
          },
          {
            text: this.$t('subscription_loyalcardsRemoteTopups'),
            show: true,
          },
        ],
      },
      subscriptionTypeActive: {
        free: true,
        basic: false,
        premium: false,
      },
      subscriptionContentActive: {
        free: true,
        basic: false,
        premium: false,
      },
      subscriptionLengthActive: {
        1: true,
        3: false,
        6: false,
        12: false,
      },
      pricesForMonthCache: {},
      calculatedSubscription: null,
    };
  },
  computed: {
    ...mapGetters({
      currencySymbol: 'auth/userCurrencySymbol',
      user: 'auth/getUser',
      countryCode: 'auth/countryCode',
    }),
    columnsWidth() {
      // max 4 columns in one row if less fit to full width
      const lengthCount = Object.keys(this.subscriptionPlans.free).length;

      if (lengthCount > 4) {
        return 3;
      }

      return 12 / lengthCount;
    },
  },
  watch: {
    dialog(val) {
      if (!val) {
        // custom method on close dialog by clicking outside
        this.onClose();
      }
    },
  },
  created() {
    if (this.paymentId) {
      this.step = 4;
      setTimeout(this.getLastPaymentData(this.paymentId), 1500);
    }
  },
  mounted() {
    this.getPackagesData();
    this.checkInvoiceData();
  },
  methods: {
    goToCheckInvoiceData() {
      this.step = 3;
    },
    initSubscription() {
      this.axios.post(
        `/api/subscriptions/package/${this.planId}/order`,
      )
        .then((response) => {
          if (response.data && response.data.externalPayment.redirectUrl) {
            window.location = `${response.data.externalPayment.redirectUrl}`;
          } else {
            this.loading = false;
          }
        })
        .catch((data) => {
          const { response } = data;
          if (response.status >= 400) {
            if (typeof response.data.message !== 'undefined') {
              this.errorMessage = response.data.message;
              this.step = 6;
            }
            this.loading = false;
          }
          this.loading = false;
        });
    },
    onClose() {
      setTimeout(() => {
        // waits till modal hide
        this.step = 1;
        this.subscriptionType = 'free';
        this.errorMessage = null;
        this.planId = null;
      }, 300);
    },
    getPackagesData() {
      this.loading = true;
      this.axios.get('/api/subscriptions/plans')
        .then((response) => {
          if (response.data) {
            this.subscriptionPlans = response.data;
            this.setSubscriptionTypes();
            this.loading = false;
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
      this.loading = false;
    },
    setSubscriptionTypes() {
      const newSubscriptiontype = {};

      if (typeof this.subscriptionPlans.free[1] === 'undefined') {
        this.subscriptionLength = 6;
      }

      newSubscriptiontype.free = {
        type: 'free',
        price: this.subscriptionPlans.free[this.subscriptionLength].value,
        planId: this.subscriptionPlans.free[this.subscriptionLength].id,
        currencySymbol: this.subscriptionPlans.free[this.subscriptionLength].currencySymbol,
      };

      newSubscriptiontype.basic = {
        type: 'basic',
        price: this.subscriptionPlans.basic[this.subscriptionLength].value,
        planId: this.subscriptionPlans.basic[this.subscriptionLength].id,
        currencySymbol: this.subscriptionPlans.basic[this.subscriptionLength].currencySymbol,
      };

      newSubscriptiontype.premium = {
        type: 'premium',
        price: this.subscriptionPlans.premium[this.subscriptionLength].value,
        planId: this.subscriptionPlans.premium[this.subscriptionLength].id,
        currencySymbol: this.subscriptionPlans.premium[this.subscriptionLength].currencySymbol,
      };

      this.subscriptionTypes = newSubscriptiontype;
    },
    getLastPaymentData(id) {
      this.axios.get(
        `/api/subscriptions/payment/${id}`,
        {},
      )
        .then((response) => {
          if (response.data) {
            this.paymentData = response.data;
            this.step = 5;
          }
        });
    },
    toggleSubscriptionType(key) {
      this.subscriptionType = key;
      this.subscriptionTypeActive.free = false;
      this.subscriptionTypeActive.basic = false;
      this.subscriptionTypeActive.premium = false;

      this.subscriptionTypeActive[key] = true;

      this.toggleSubscriptionContent(key);
    },
    toggleSubscriptionLength(key, planId) {
      this.planId = planId;

      this.subscriptionLengthActive[1] = false;
      this.subscriptionLengthActive[3] = false;
      this.subscriptionLengthActive[6] = false;
      this.subscriptionLengthActive[12] = false;

      this.subscriptionLengthActive[key] = true;

      this.recalculateSubscription();
    },
    toggleSubscriptionContent(key) {
      this.subscriptionContentActive.free = false;
      this.subscriptionContentActive.basic = false;
      this.subscriptionContentActive.premium = false;

      if (key === 'free') {
        this.subscriptionContentActive.free = true;
        // this.onePersonContentToggle(true);
      } else if (key === 'basic') {
        this.subscriptionContentActive.free = true;
        this.subscriptionContentActive.basic = true;
        // this.onePersonContentToggle(false);
      } else {
        this.subscriptionContentActive.free = true;
        this.subscriptionContentActive.basic = true;
        this.subscriptionContentActive.premium = true;
        // this.onePersonContentToggle(false);
      }
    },
    onePersonContentToggle(show) {
      this.subscriptionContents.free[0].show = show;
    },
    gotToSubscriptionLength() {
      this.loading = true;
      this.step = 2;
      this.recalculateSubscription();
      this.loading = false;
    },
    recalculateSubscription() {
      this.axios.get(`/api/subscriptions/package/${this.planId}/calculate`)
        .then((response) => {
          if (response.data) {
            this.calculatedSubscription = response.data;
          }
        })
        .catch(() => {
        });
    },
    stepBack() {
      this.step -= 1;
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    checkInvoiceData() {
      this.axios.get('/cm/profile/invoice_data_check')
        .then((response) => {
          if (response.data) {
            this.isDataCompleted = response.data.isDataCompleted;
          }
        })
        .catch(() => {
        });
    },
    goToPeriodChose() {
      // if free subscription nothing to do here, close modal
      if (this.subscriptionType === 'free') {
        this.dialog = false;
      } else {
        this.planId = this.subscriptionTypes[this.subscriptionType].planId;
        this.loading = true;
        this.gotToSubscriptionLength();
      }
    },
  },
};
</script>

<style lang="stylus">
.subscriptionName
  font-size 12px

.paysystemLogo
  height 50px
  margin-top 8px

.container-active
  border 2px solid rgb(72, 167, 242) !important

  .v-icon
    background-color rgba(72, 167, 242, 0.65) !important
    font-size 13px !important

.container-inactive
  background rgba(173, 165, 165, 0.161) !important
  border 2px solid rgba(173, 165, 165, 0.01) !important

  .v-icon
    background-color rgba(244, 67, 54, 0.65) !important
    font-size 13px !important

.subscription-content-option
  display flex
  align-items center
  border-bottom 1px solid rgba(0, 0, 0, 0.12)
  border-bottom-right-radius 0px !important
  border-bottom-left-radius 0px !important
  text-align left

  .v-icon
    background-repeat no-repeat
    border-bottom-left-radius 50%
    border-bottom-right-radius 50%
    border-top-left-radius 50%
    border-top-right-radius 50%
    font-size 16px
    font-weight bold
    margin-right 10px
    margin-top 4px
    margin-bottom 4px

.loader
  top 35%
  z-index 5

.loader-background
  text-align: center;
  width 100%
  height 100px
  z-index 4
</style>
