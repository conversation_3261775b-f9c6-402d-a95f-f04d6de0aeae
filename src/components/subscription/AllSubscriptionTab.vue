<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <subscription-filters v-model="filters" />
    <v-row>
      <v-col
        cols="12"
      >
        <subscription-list
          :url="'/administration/subscriptions'"
          :filters="filters"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import SubscriptionList from '@components/subscription/SubscriptionList.vue';
import SubscriptionFilters from '@components/subscription/SubscriptionFilters.vue';

export default {
  components: {
    SubscriptionList,
    SubscriptionFilters,
  },
  data() {
    return {
      filters: {},
    };
  },
};
</script>
