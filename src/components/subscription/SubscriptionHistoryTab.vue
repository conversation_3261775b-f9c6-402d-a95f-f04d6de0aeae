<template>
  <div>
    <v-card>
      <v-toolbar
        dark
        flat
        color="secondary"
      >
        <v-btn
          icon
          dark
        >
          <v-icon>mdi-list-box</v-icon>
        </v-btn>
        <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
          {{ $t('subscription_historyList') }}
        </v-toolbar-title>
        <v-spacer />
      </v-toolbar>
      <v-card-text>
        <v-layout
          row
          wrap
        >
          <v-col cols="12">
            <v-card-text dark>
              <v-data-table
                :headers="dataTable.headers"
                :items="dataTable.items"
                item-key="number"
                :loading="loader"
                :options.sync="pagination"
                :no-data-text="$t('common_noData')"
                class="text-uppercase"
                :footer-props="dataTable.footerProps"
              >
                <template #progress>
                  <div class="text-center">
                    <v-progress-circular
                      class="loader"
                      indeterminate
                      color="primary"
                    />
                  </div>
                </template>

                <template #item="{ item}">
                  <template v-if="!loader">
                    <tr>
                      <td class="text-sm-start">
                        {{ item.startDate|formatDateDay }}
                      </td>
                      <td class="text-sm-start">
                        {{ item.endDate|formatDateDay }}
                      </td>
                      <td class="text-sm-start">
                        <div v-if="item.status === 'paid'">
                          <v-icon
                            class="material-icons"
                            color="success"
                          >
                            mdi-checkbox-marked
                          </v-icon>
                          <span class="success--text">{{ $t('common_paid') }}</span>
                        </div>
                        <div v-else-if="item.status === 'initiated'">
                          <v-icon
                            class="material-icons"
                            color="progress"
                          >
                            mdi-cached
                          </v-icon>
                          <span
                            class="progress--text"
                          >
                            {{ $t('common_processing') }}
                          </span>
                        </div>
                        <div v-else-if="item.status === 'initiated_proforma'">
                          <v-icon
                            class="material-icons"
                            color="progress"
                          >
                            mdi-cached
                          </v-icon>
                          <span
                            class="progress--text"
                          >
                            {{ $t('common_processing') }}
                          </span>
                        </div>
                        <div
                          v-else-if="item.status === 'canceled'
                            || item.status === 'manually_canceled'"
                        >
                          <v-icon
                            class="material-icons"
                            color="error"
                          >
                            mdi-alert
                          </v-icon>
                          <span class="error--text">{{ $t('common_canceled') }}</span>
                        </div>
                        <div v-else-if="item.status === 'to_pay'">
                          <v-icon
                            class="material-icons"
                            color="warning"
                          >
                            mdi-credit-card
                          </v-icon>
                          <span class="warning--text">{{ $t('subscription_toPay') }}</span>
                        </div>
                      </td>
                      <td class="text-sm-start">
                        {{
                          $t(`subscriptions.types.${item.type}`)
                        }}
                      </td>
                      <td class="text-sm-end">
                        {{
                          Number(item.grossValue)|currencySymbol(item.currencySymbol)
                        }}
                      </td>
                    </tr>
                  </template>
                </template>
              </v-data-table>
            </v-card-text>
          </v-col>
        </v-layout>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      loader: true,
      pagination: {
        page: 1,
        itemsPerPage: 10,
        sortBy: ['startDate'],
        sortDesc: [true],
      },
      dataTable: {
        headers: [
          {
            text: this.$t('common_startDate'),
            value: 'startDate',
            class: 'text-sm-start',
            showInRowExpand: true,
          },
          {
            text: this.$t('common_subscriptionsEnddate'),
            value: 'endDate',
            class: 'text-sm-start',
            showInRowExpand: true,
          },
          {
            text: this.$t('subscription_state'),
            value: 'success',
            class: 'text-sm-start',
            showInRowExpand: true,
          },
          {
            text: this.$t('subscriptions.subscription'),
            value: 'type',
            class: 'text-sm-start',
            showInRowExpand: true,
          },
          {
            text: this.$t('common_price'),
            value: 'grossValue',
            class: 'text-sm-end',
            showInRowExpand: true,
          },
        ],
        items: [],
        totalItems: 0,
        footerProps: {
          'items-per-page-options': [5, 10, 25],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      currency: 'auth/userCurrencySymbol',
      user: 'auth/getUser',
    }),
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.loader = true;
      this.axios.get('/api/subscriptions')
        .then((response) => {
          if (response.data) {
            this.dataTable.items = response.data.data;
            this.dataTable.totalItems = response.data.total;
            this.loader = false;
          }
          this.loader = false;
        })
        .catch(() => {
          this.loader = false;
        });
    },
  },
};
</script>
