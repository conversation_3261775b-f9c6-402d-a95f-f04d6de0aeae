<template>
  <report-data-table
    ref="table"
    :headers="headers"
    :url="url"
    :filters="filters"
  >
    <template #[`item.ctime`]="{ item }">
      {{ item.ctime|formatDateDayTimeWithSeconds }}
    </template>
    <template #[`item.ownerEmail`]="{ item }">
      {{ item.subscriber.name ? item.subscriber.name : '-' }}
    </template>
    <template #[`item.grossValue`]="{ item }">
      {{ item.grossValue|currencySymbol(item.currencySymbol) }}
    </template>
    <template #[`item.startDate`]="{ item }">
      {{ item.startDate|formatDateDay }}
    </template>
    <template #[`item.endDate`]="{ item }">
      {{ item.endDate|formatDateDay }}
    </template>
    <template #[`item.status`]="{ item }">
      <status-text :status="item.status" />
    </template>
    <template #[`item.vatTax`]="{ item }">
      {{ item.vatTax ? item.vatTax.taxValue : '-' }} %
    </template>
    <template #[`item.actions`]="{ item }">
      <div class="display-flex">
        <subscription-preview-modal
          :subscription-id="item.id"
        />
        <subscription-confirm-modal
          :key="`subscription-confirm-${item.id}`"
          :subscription-id="item.id"
          :item="item"
          :on-success="refresh"
        />
        <subscription-delete-modal
          :key="`subscription-delete-${item.id}`"
          :item="item"
          :on-success="refresh"
        />
      </div>
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@components/reports/ReportDataTable.vue';
import StatusText from '@components/subscription/fields/StatusText.vue';
import SubscriptionConfirmModal from '@components/subscription/modals/SubscriptionConfirmModal.vue';
import SubscriptionDeleteModal from '@components/subscription/modals/SubscriptionDeleteModal.vue';
import SubscriptionPreviewModal from '@components/subscription/modals/SubscriptionPreviewModal.vue';

export default {
  components: {
    StatusText,
    SubscriptionConfirmModal,
    SubscriptionDeleteModal,
    SubscriptionPreviewModal,
    ReportDataTable,
  },
  props: {
    onFiltersChange: {
      type: Function,
      default: () => {},
    },
    onLoading: {
      type: Function,
      default: () => {},
    },
    url: {
      type: String,
      required: true,
    },
    filters: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      headers: [
        {
          text: this.$t('common_id'),
          value: 'id',
          sortable: false,
          width: '50',
        },
        {
          text: this.$t('admin_added'),
          value: 'ctime',
          sortable: false,
          align: 'start',
          width: '150',
        },
        {
          text: this.$t('admin_subscriber'),
          value: 'ownerEmail',
          sortable: false,
          width: '200',
        },
        {
          text: this.$t('common_startDate'),
          value: 'startDate',
          width: '100',
          sortable: false,
        },
        {
          text: this.$t('common_subscriptionsEnddate'),
          value: 'endDate',
          width: '100',
          sortable: false,
        },
        {
          text: this.$t('common_type'),
          value: 'type',
          sortable: false,
          width: '100',
        },
        {
          text: this.$t('common_state'),
          value: 'status',
          sortable: false,
          width: '50',
        },
        {
          text: this.$t('common_price'),
          value: 'grossValue',
          class: 'text-sm-end',
          sortable: false,
          align: 'end',
          width: '100',
        },
        {
          text: this.$t('admin_vat'),
          value: 'vatTax',
          class: 'text-sm-end',
          sortable: false,
          width: '80',
        },
        {
          text: this.$t('admin_whoAdded'),
          value: 'whoAddedEmail',
          sortable: false,
          width: '150',
        },
        {
          text: this.$t('common_comment'),
          value: 'comment',
          sortable: false,
        },
        {
          text: this.$t('actions.actions'),
          class: 'text-sm-end',
          value: 'actions',
          align: 'right',
          sortable: false,
          width: '190',
        },
      ],
    };
  },
  methods: {
    refresh() {
      this.$refs.table.fetchData();
    },
  },
};
</script>

<style>
.display-flex {
  display: flex;
  grid-template-columns: auto 23px 23px;
  grid-template-rows: auto auto;
}
</style>
