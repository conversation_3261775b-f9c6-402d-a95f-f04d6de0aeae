<template>
  <div>
    <table class="pt-2 summary-table">
      <tr>
        <td>
          {{ $t('subscription.table.position') }}
        </td>
        <td>
          {{ $t('subscription.table.type') }}
        </td>
        <td class="text-end">
          {{ $t('common_document') }}
        </td>
        <td class="text-end">
          {{ $t('subscription.table.price-before-discount') }}
        </td>
      </tr>
      <tr
        v-for="(it, index) in subscriptionCalculation.item"
        :key="index"
      >
        <td>
          {{ it.position }}
        </td>
        <td
          :class="`${subscriptionCarwashType[it.type].color}--text`"
        >
          {{ $t(subscriptionCarwashType[it.type].text) }}
        </td>
        <td class="text-end">
          {{ it.invoice ?? '-' }}
        </td>
        <td class="text-end">
          {{ it.totalPrice|currencySymbol(currencySym) }}
        </td>
      </tr>
      <tr class="font-weight-bold">
        <td />
        <td />
        <td>
          {{ $t('subscription.table.sum') }}
        </td>
        <td class="text-end">
          {{
            subscriptionCalculation.baseValue|currencySymbol(currencySym)
          }}
        </td>
      </tr>
    </table>

    {{ $t('subscription.table.summary') }}
    <table class="pt-2 summary-table">
      <tr class="font-weight-bold">
        <td>
          {{ $t('subscription.table.type') }}
        </td>
        <td>
          {{ $t('subscription.table.price-before-discount') }}
        </td>
        <td>
          {{ $t('subscription.table.discount') }}
        </td>
        <td class="text-end">
          {{ $t('subscription.table.price-after-discount') }}
        </td>
      </tr>
      <tr
        v-for="(summary, index) in subscriptionCalculation.summary"
        :key="index"
      >
        <td>
          {{ $t(subscriptionType[summary.type].text) }}
        </td>
        <td class="text-end">
          {{ summary.baseValue|currencySymbol(currencySym) }}
        </td>
        <td class="text-end">
          {{ summary.discount }}%
        </td>
        <td class="font-weight-bold text-end">
          {{ summary.valueAfterDiscount|currencySymbol(currencySym) }}
        </td>
      </tr>
    </table>
  </div>
</template>

<script>
import { SubscriptionCarwashType, SubscriptionType } from '@components/subscription/types';

export default {
  props: {
    subscriptionCalculation: {
      type: Object,
      default: () => {},
      required: true,
    },
    currencySym: {
      type: String,
      required: true,
    },
  },
  computed: {
    subscriptionType() {
      return SubscriptionType;
    },
    subscriptionCarwashType() {
      return SubscriptionCarwashType;
    },
  },
};
</script>
