<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search v-model="search" />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import TextSearch from '@components/common/filters/TextSearch.vue';

export default {
  components: {
    TextSearch,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      search: this.param.search ?? null,
    };
  },
};
</script>
