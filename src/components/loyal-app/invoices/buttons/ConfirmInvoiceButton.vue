<template>
  <btn-confirm
    v-if="invoice.status === 'pending'"
    small
    :confirmed="false"
    :confirm-hint-text="$t('loyalApp_confirmPaymentAndInvoie')"
    @confirm="confirmInvoice"
  />
</template>

<script>
import BtnConfirm from '@/components/common/BtnConfirm.vue';

export default {
  components: {
    BtnConfirm,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    invoice: {
      type: Object,
      required: true,
    },
  },
  methods: {
    async confirmInvoice() {
      const url = `/api/loyalapp/invoice/${this.invoice.id}/confirm?app=${this.app}`;

      await this.axios.post(url, {
        status: 'confirmed',
      });

      this.$emit('confirmed');
    },
  },
};
</script>
