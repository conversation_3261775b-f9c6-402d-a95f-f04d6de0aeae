<template>
  <v-tooltip bottom>
    <template #activator="{ on }">
      <v-btn
        class="ml-2"
        x-small
        tile
        rounded
        fab
        elevation="1"
        color="primary"
        v-on="on"
        @click.stop
        @click.native="sendInvoiceEmail"
      >
        <v-icon>mdi-email</v-icon>
      </v-btn>
    </template>
    <span>{{ $t('actions.send_invoice') }}</span>
  </v-tooltip>
</template>

<script>
export default {
  props: {
    app: {
      type: String,
      required: true,
    },
    invoice: {
      type: Object,
      required: true,
    },
  },
  methods: {
    async sendInvoiceEmail() {
      const url = `/api/gateway/wla-admin/invoice/${this.invoice.id}/send`;

      await this.axios.get(url, {
        params: {
          app: this.app,
        },
      });

      this.$emit('sent');
    },
  },
};
</script>
