<template>
  <v-tooltip bottom>
    <template #activator="{ on, attrs }">
      <v-icon
        :color="getColor(status)"
        v-bind="attrs"
        v-on="on"
      >
        {{ getIcon(status) }}
      </v-icon>
    </template>
    <span> {{ getText(status) }} </span>
  </v-tooltip>
</template>

<script>

export default {
  name: 'StatusIcon',
  props: {
    status: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      icons: {
        canceled: 'mdi-close-circle-outline',
        confirmed: 'mdi-check-circle-outline',
        default: 'mdi-help-circle-outline',
        error: 'mdi-close-octagon-outline',
        initiated: 'mdi-progress-star',
        pending: 'mdi-progress-clock',
        refunded: 'mdi-credit-card-refund-outline',
        rejected: 'mdi-alert-outline',
        timeout: 'clock-alert-outline',
        waiting: 'mdi-progress-clock',
      },
      colors: {
        canceled: 'error',
        confirmed: 'green darken-2',
        default: 'gray',
        error: 'error',
        initiated: 'progress',
        pending: 'progress',
        refunded: 'warning',
        rejected: 'error',
        timeout: 'error',
        waiting: 'progress',
      },
      texts: {
        canceled: this.$t('common_canceled'),
        confirmed: this.$t('common_confirmed'),
        default: this.$t('common_unknown'),
        error: this.$t('common_error'),
        initiated: this.$t('common_initiated'),
        pending: this.$t('common_pending'),
        refunded: this.$t('common_refund'),
        rejected: this.$t('common_rejected'),
        timeout: this.$t('common_timeout'),
        waiting: this.$t('common_waiting'),
      },
    };
  },
  methods: {
    getIcon(status) {
      if (status in this.icons) {
        return this.icons[status];
      }
      return 'mdi-help';
    },
    getColor(status) {
      if (status in this.icons) {
        return this.colors[status];
      }
      return 'grey';
    },
    getText(status) {
      if (status in this.texts) {
        return this.texts[status];
      }
      return status;
    },
  },
};
</script>
