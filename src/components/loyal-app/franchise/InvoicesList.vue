<template>
  <report-data-table
    :title="$t('common_invoices')"
    :headers="headers"
    :url="url"
  >
    <template #[`item.time`]="{ item }">
      {{ item.invoiceDate|formatDateDayTime }}
    </template>

    <template #[`item.totalGross`]="{ item }">
      {{ item.totalGross|currencySymbol(item.currency) }}
    </template>

    <template #[`item.confirmaDate`]="{ item }">
      <btn-confirm2
        :confirmed="item.confirmDate !== null"
        :confirm-date="item.confirmDate"
        :confirm-user="item.confirmEmail"
        :url="`/api/gateway/wla-owner/invoice/${item.id}/confirm`"
      />
    </template>

    <template #[`item.downloadUrl`]="{ item }">
      <act-download
        :text="$t('downloadInvoice')"
        :url="`/api/gateway/wla-owner/invoice/${item.id}/download`"
      />
      <act-download
        :text="$t('common_downloadReport')"
        :url="`/api/gateway/wla-owner/invoice/${item.id}/attachment`"
      />
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@components/reports/ReportDataTable.vue';
import ActDownload from '@components/common/Action/ActDownload.vue';
import BtnConfirm2 from '@components/common/BtnConfirm2.vue';

export default {
  components: {
    BtnConfirm2,
    ActDownload,
    ReportDataTable,
  },
  data() {
    return {
      url: '/api/finance/selfinvoices',
      headers: [
        {
          value: 'invoiceDate',
          text: this.$t('common_mobilePaymentInvoicesDate'),
        },
        {
          value: 'period',
          text: this.$t('common_period'),
          sortable: false,
        },
        {
          value: 'number',
          text: this.$t('common_invoiceNumber'),
          sortable: false,
          align: 'start',
        },
        {
          value: 'totalGross',
          text: this.$t('common_valueGross'),
          sortable: false,
          align: 'end',
        },
        {
          value: 'confirmaDate',
          text: this.$t('common_confirmation'),
          sortable: false,
          align: 'center',
          width: '130px',
        },
        {
          value: 'downloadUrl',
          text: ' ',
          sortable: false,
          align: 'end',
          width: '130px',
        },
      ],
    };
  },
};
</script>
