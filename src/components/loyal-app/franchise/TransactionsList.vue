<template>
  <div>
    <v-row>
      <v-col cols="4">
        <carwash-select
          v-model="carwash"
        />
      </v-col>
      <v-col cols="4">
        <date-select
          :show-custom="false"
          @change="onDateRangeChange"
        />
      </v-col>
    </v-row>
    <report-data-table
      :title="$t('transactions.history')"
      :headers="headers"
      :query-params="filters"
      :url="url"
    >
      <template #[`item.time`]="{ item }">
        <div class="flex-inline-start">
          <status-badge :status="item.status" />
          {{ item.time|formatDateDayTime }}
        </div>
      </template>

      <template #[`item.value`]="{ item }">
        {{ item.value|currencySymbol(item.currency) }}
      </template>
    </report-data-table>
  </div>
</template>

<script>
import ReportDataTable from '@components/reports/ReportDataTable.vue';
import StatusBadge from '@components/loyal-app/franchise/StatusBadge.vue';
import CarwashSelect from '@components/common/page/selects/CarwashSelect.vue';
import DateSelect from '@components/common/page/selects/DateSelect.vue';

export default {
  components: {
    DateSelect,
    CarwashSelect,
    ReportDataTable,
    StatusBadge,
  },
  data() {
    return {
      carwash: null,
      startDate: null,
      endDate: null,
      interval: 'thisWeek',
      url: '/api/finance/transactions',
      headers: [
        {
          value: 'time',
          text: this.$t('finance_date'),
          sortable: false,
        },
        {
          value: 'carwashName',
          text: this.$t('finance_carwash'),
          sortable: false,
          align: 'start',
        },
        {
          value: 'standCode',
          text: this.$t('finance_standCode'),
          sortable: false,
        },
        {
          value: 'value',
          text: this.$t('finance_value'),
          sortable: false,
          align: 'end',
        },
      ],
    };
  },
  computed: {
    filters() {
      return {
        serial: this.carwash,
        interval: this.interval,
      };
    },
  },
  methods: {
    onDateRangeChange(dateRange) {
      this.interval = dateRange.value;
    },
  },
};
</script>
