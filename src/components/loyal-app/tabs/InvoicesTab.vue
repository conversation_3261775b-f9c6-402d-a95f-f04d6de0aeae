<template>
  <div>
    <invoices-filters v-model="filters" />
    <invoices-list
      ref="dataTable"
      :app="app"
      :params="filters"
    />
  </div>
</template>

<script>
import InvoicesFilters from '@components/loyal-app/invoices/InvoicesFilters.vue';
import InvoicesList from '@components/loyal-app/invoices/InvoicesList.vue';

export default {
  components: {
    InvoicesFilters,
    InvoicesList,
  },
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      loading: false,
      filters: {},
    };
  },
  watch: {
    app() {
      this.fetchData();
    },
    filters: {
      handler(newVal) {
        localStorage.setItem('loyal-app-invoices-filter', JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  created() {
    const savedFilter = localStorage.getItem('loyal-app-invoices-filter');
    if (savedFilter) {
      this.filters = JSON.parse(savedFilter);
    }
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};

</script>
