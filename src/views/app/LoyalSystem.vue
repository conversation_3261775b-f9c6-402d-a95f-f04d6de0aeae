<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <img
        alt="logo"
        class="logo"
        src="@assets/logobkfpay.png"
      >
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('common_loyalsystem') }}
      </v-toolbar-title>
      <v-spacer />
    </v-toolbar>
    <v-card-text class="pa-0 mt-0">
      <v-tabs
        show-arrows
        dens
        dark
        background-color="secondary lighten-1"
      >
        <v-tab
          v-for="(item) in tabs"
          :key="item.key"
          ripple
          :disabled="!item.show"
        >
          {{ item.text }}
        </v-tab>
        <v-tab-item
          v-for="(item) in tabs"
          :key="item.key"
          class="pa-4"
        >
          <component
            :is="item.component"
            v-bind="item.props"
          />
        </v-tab-item>
      </v-tabs>
    </v-card-text>
  </v-card>
</template>

<script>
import CardsTab from '@components/loyalty-cards/tabs/CardsTab.vue';
import TopUpsTab from '@components/loyalty-cards/tabs/TopUpsTab.vue';
import ClientList from '@components/loyalty-cards/clients/ClientList.vue';
import CyclicTopUps from '@components/loyalty-cards/cyclicTopUps/CyclicTopUps.vue';
import ConfigurationPage from '@components/loyalty-cards/config/ConfigurationPage.vue';
import Invoices from '@components/loyalty-cards/invoices/Invoices.vue';
import { mapGetters } from 'vuex';
import PaymentsList from '@components/loyalty-cards/payments/PaymentsList.vue';
import LoyalCardsSubscriptionPackages from '@components/loyalty-cards/subscriptions/LoyalCardsSubscriptionPackages.vue';
import CarwashesList from '@components/loyalty-cards/carwashes/CarwashesList.vue';
import TransactionsTab from '@components/loyalty-cards/tabs/TransactionsTab.vue';

export default {
  components: {
    CardsTab,
    TransactionsTab,
    TopUpsTab,
    CyclicTopUps,
    ClientList,
    CarwashesList,
    ConfigurationPage,
    Invoices,
    PaymentsList,
    LoyalCardsSubscriptionPackages,
  },
  data() {
    return {
      error: false,
      logout: false,
      applicationError: false,
    };
  },
  computed: {
    ...mapGetters({
      canAccess: 'auth/canAccess',
    }),
    tabs() {
      return [
        {
          component: CardsTab,
          key: 'cards',
          props: {
            showFiltering: true,
          },
          show: true,
          text: this.$t('common_cards'),
        },
        {
          component: TransactionsTab,
          key: 'transactions2',
          props: {
            autoLoad: true,
            autoUpdateTransactions: true,
            showFiltering: true,
          },
          show: true,
          text: this.$t('card.transactions'),
        },
        {
          component: TopUpsTab,
          key: 'top-ups',
          props: {
            autoLoad: true,
            autoUpdateTransactions: true,
            showFiltering: true,
          },
          show: true,
          text: this.$t('common_topups'),
        },
        {
          component: CyclicTopUps,
          key: 'cyclic-top-ups',
          props: {
            autoLoad: true,
            autoUpdateTransactions: true,
            showFiltering: true,
          },
          show: this.canAccess('loyalty', 'cyclicTopUps'),
          text: this.$t('common_cyclicTopUpsHeading'),
        },
        {
          component: ClientList,
          key: 'clients',
          props: {
            autoLoad: true,
            showFiltering: true,
          },
          show: this.canAccess('loyalty', 'clients'),
          text: this.$t('loyaltyCards_clients'),
        },
        {
          component: Invoices,
          key: 'invoices',
          props: {
            autoLoad: true,
            autoUpdateTransactions: true,
            showFiltering: true,
          },
          show: this.canAccess('loyalty', 'invoices'),
          text: this.$t('common_invoices_heading'),
        },
        {
          component: PaymentsList,
          key: 'payments',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
          },
          show: this.canAccess('loyalty', 'transactions'),
          text: this.$t('loyaltyCards_payments'),
        },
        {
          component: ConfigurationPage,
          key: 'invoice-data',
          props: {},
          show: this.canAccess('loyalty', 'cards'),
          text: this.$t('loyaltyCards_settings'),
        },
        {
          text: this.$t('loyalSystem_packages'),
          component: 'loyal-cards-subscription-packages',
          key: 'subscription-packages',
          show: this.canAccess('loyalty', 'cards'),
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
          },
        },
        {
          component: CarwashesList,
          key: 'carwashes',
          props: {
            showFiltering: true,
          },
          show: true,
          text: this.$t('common_carwashes'),
        },
      ];
    },
  },
};
</script>

<style scoped>
  .logo {
    height: 26px;
    padding-right: 10px;
  }
</style>
