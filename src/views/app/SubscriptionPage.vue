<template>
  <div class="widget-container vue-card subscriptions">
    <v-row
      v-if=" carwashes.length == 0"
      wrap
    >
      <v-col>
        <v-alert
          class="no-margin"
          border="left"
          :value="true"
          type="info"
        >
          <div class="info-content">
            <span class="text">{{ $t('subscription_noCarwashAttached') }}</span>
          </div>
        </v-alert>
      </v-col>
    </v-row>
    <v-row
      row
      wrap
    >
      <v-col
        md="6"
        cols="12"
      >
        <user-subscription-widget
          ref="your-subscription-widget"
          :disabled="carwashes.length == 0"
        />
      </v-col>
      <v-col
        md="6"
        cols="12"
      >
        <subscription-history-tab ref="subscription-history-widget" />
      </v-col>
    </v-row>
  </div>
</template>

<script>
import UserSubscriptionWidget from '@components/subscription/UserSubscriptionWidget.vue';
import SubscriptionHistoryTab from '@components/subscription/SubscriptionHistoryTab.vue';
import { mapGetters } from 'vuex';

export default {
  components: {
    SubscriptionHistoryTab,
    UserSubscriptionWidget,
  },
  data() {
    return {
      infoColor: '#f5a623',
      logout: false,
      applicationError: false,
      isDataCompleted: true,
    };
  },
  computed: {
    ...mapGetters({
      currencySymbol: 'auth/userCurrencySymbol',
      carwashes: 'carwashes/carwashes',
    }),
  },
};
</script>

<style lang="stylus" scoped>
.alert
  width 100%;
  margin: 0 4px 10px 4px;

#app .alert.info
  background-color: rgba(245, 165, 36, 0.77) !important;

.info-content
  display flex;
  justify-content space-between;
  align-items baseline;

#app .text
  font-size 16px;
  text-align center;
  color white;
  flex-basis 80%;
  text-shadow: 1px 0px 0px #fff;

.card .title h5
  text-align left

#app .subscriptions
  .container.grid-list-md .layout .flex:not(:last-child)
    padding-right 26px

  .container.grid-list-md
    padding-top 0px

@media only screen and (max-width: 840px)
  .info-content
    margin-top 10px;
    flex-direction column;
    align-items center;

  .info-content > .btn
    margin-top 15px;
</style>
